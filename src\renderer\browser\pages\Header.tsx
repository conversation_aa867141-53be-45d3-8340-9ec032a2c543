import { useEffect, useState, useRef } from 'react'
import { Tabs } from './Tabs'
import { Actions } from './Actions'
import { browserAuthorizeChannel, browserChannel } from '@common/events/browser-events'
import type { BrowserTabsData } from '@common/structure/browser/header'
import { useTabStore } from '@browser/store/tabStore'
import type { ContextFavorite } from '@common/structure/space/context-favorite'
import { useFavoriteStore } from '@browser/store/favoriteStore'
import { RpaActions } from './rpaActions'
import InfoIcon from '@browser/assets/info.svg?react'
import { useBrowserService } from '@browser/infrastructure/services/browser-service'

export function Header() {
  const { activeTab, setTabs, setActiveTab } = useTabStore()
  const { setFavorites } = useFavoriteStore()

  const [pending, setPending] = useState(false)
  const [showGuide, setShowGuide] = useState(false)
  const [hasShownGuideOnce, setHasShownGuideOnce] = useState(false) // 记录是否已经显示过引导

  const [showCloseConfirm, setShowCloseConfirm] = useState(false) // 控制关闭确认弹窗显示

  const [authorizeErrorMessage, setAuthorizeErrorMessage] = useState<string | null>(null)

  const browserService = useBrowserService()

  // 防抖：记录最后一次点击时间
  const lastClickTimeRef = useRef<number>(0)

  useEffect(() => {
    window.api.send(browserChannel.headerInitialed)
  }, [])

  useEffect(() => {
    setAuthorizeErrorMessage(null)
    // 当页面可以授权时，检查是否需要显示功能引导
    if (activeTab?.canAuthorize) {
      // 无论是否隐藏引导，都要标记已经遇到过可授权页面
      setHasShownGuideOnce(true)
      // 同时在localStorage中记录，确保刷新后仍然有效
      localStorage.setItem('hasSeenAuthPage', 'true')

      // 只有在未设置"不再提示"时才显示引导弹窗
      if (!localStorage.getItem('hideAuthGuide')) {
        setShowGuide(true)
      }
    }
    return window.api.send(browserChannel.resetHeaderHeight)
  }, [activeTab?.canAuthorize])

  useEffect(() => {
    return window.api.on(
      browserChannel.headerUpdated,
      (_event, data: BrowserTabsData) => {
        console.log('browserChannel.headerUpdated', data)
        setTabs(data.tabs)
        setActiveTab(data.activeTab)
      },
      false,
    )
  }, [setTabs, setActiveTab])

  useEffect(() => {
    return window.api.on(
      browserChannel.syncFavorites,
      (_event, contextFavorites: Record<string, ContextFavorite[]>) => {
        console.log('browserChannel.syncFavorites', contextFavorites)
        setFavorites(contextFavorites)
      },
      false,
    )
  }, [setFavorites])

  useEffect(() => {
    return window.api.on(browserAuthorizeChannel.accountAuthorizing, () => {
      console.log('accountAuthorizing')
    })
  }, [])

  useEffect(() => {
    return window.api.on(browserAuthorizeChannel.accountAuthorizeSuccess, () => {
      console.log('accountAuthorizeSuccess')
    })
  }, [])

  useEffect(() => {
    return window.api.on(browserAuthorizeChannel.accountAuthorizeError, () => {
      console.log('accountAuthorizeError')
    })
  }, [])

  // 监听 tab 视图被置顶的事件，确保弹窗始终在最上层
  useEffect(() => {
    return window.api.on(browserChannel.tabViewTopped, () => {
      console.log('tabViewTopped - checking if modal needs to stay on top')
      // 只有在引导弹窗或关闭确认弹窗显示时，才重新设置 header 视图到顶层
      if (showGuide || showCloseConfirm) {
        console.log('弹窗显示中，重新设置 header 视图到顶层')
        window.api.send(browserChannel.setHeaderViewTop)
      } else {
        console.log('弹窗未显示，允许网页视图保持置顶')
      }
    })
  }, [showGuide, showCloseConfirm])

  // 控制视图层级：显示引导弹窗时确保 Header 视图在最顶层
  useEffect(() => {
    if (showGuide) {
      // 显示引导弹窗时，确保 Header 视图在最顶层
      console.log('显示引导弹窗，设置 Header 视图置顶')
      browserService.showHeaderUI()
    } else {
      // 关闭引导弹窗时恢复网页视图置顶
      console.log('关闭引导弹窗，恢复网页视图置顶')
      browserService.hideHeaderUI()

      // 额外确保网页视图置顶（防止某些情况下置顶失败）
      setTimeout(() => {
        console.log('延迟再次调用 hideHeaderUI 确保网页视图置顶')
        browserService.hideHeaderUI()
      }, 100)
    }

    // 清理函数：组件卸载时恢复视图层级
    return () => {
      if (showGuide) {
        console.log('组件卸载，恢复网页视图置顶')
        browserService.hideHeaderUI()
      }
    }
  }, [showGuide, browserService])

  // 控制视图层级：显示关闭确认弹窗时确保 Header 视图在最顶层
  useEffect(() => {
    if (showCloseConfirm) {
      // 显示关闭确认弹窗时，确保 Header 视图在最顶层
      console.log('显示关闭确认弹窗，设置 Header 视图置顶')
      browserService.showHeaderUI()
    } else {
      // 关闭确认弹窗关闭时，如果引导弹窗也没有显示，则恢复网页视图置顶
      if (!showGuide) {
        console.log('关闭确认弹窗关闭且引导弹窗未显示，恢复网页视图置顶')
        browserService.hideHeaderUI()

        // 额外确保网页视图置顶（防止某些情况下置顶失败）
        setTimeout(() => {
          console.log('延迟再次调用 hideHeaderUI 确保网页视图置顶')
          browserService.hideHeaderUI()
        }, 100)
      }
    }

    // 清理函数：组件卸载时恢复视图层级
    return () => {
      if (showCloseConfirm && !showGuide) {
        console.log('组件卸载，恢复网页视图置顶')
        browserService.hideHeaderUI()
      }
    }
  }, [showCloseConfirm, showGuide, browserService])

  // 监听窗口关闭确认检查
  useEffect(() => {
    return window.api.on(browserChannel.checkCloseConfirm, () => {
      console.log('收到关闭确认检查请求')

      // 检查是否存在"我已完成登录"按钮且已经看过授权页面
      const hasLoginButton = activeTab?.canAuthorize
      const hasSeenAuthPage = hasShownGuideOnce || localStorage.getItem('hasSeenAuthPage')

      if (hasLoginButton && hasSeenAuthPage) {
        // 存在登录按钮且看过授权页面，显示关闭确认提示
        console.log('显示关闭确认提示')
        setShowCloseConfirm(true) // 显示自定义关闭确认弹窗
      } else {
        // 没有登录按钮或未看过授权页面，直接关闭
        console.log('直接关闭浏览器')
        window.api.send(browserChannel.confirmClose, true)
      }
    })
  }, [activeTab?.canAuthorize, hasShownGuideOnce])

  return (
    <div className="flex h-full flex-col">
      <div className="flex flex-col">
        <div className="flex h-[40px] w-full flex-shrink-0 items-center bg-[#F4F6F8]">
          <Tabs></Tabs>
          <Actions></Actions>
          <RpaActions />
        </div>


        {activeTab?.canAuthorize && (
          <div className="relative">
            <div className="flex h-[34px] items-center bg-[#E8EFFF] px-4">
              <InfoIcon className="mr-1 h-5 w-5" />
              <span className="grow truncate text-xs">
                {authorizeErrorMessage ?? '请在确认登录成功后，点击右边的按钮。'}
              </span>
              <button
                className="h-6 shrink-0 cursor-pointer rounded bg-[#4F46E5] px-2 text-xs text-white hover:bg-[#4338CA] disabled:cursor-not-allowed disabled:opacity-50"
                disabled={!activeTab || pending}
                onClick={async () => {
                  try {
                    if (!activeTab || pending) return

                    // 补丁，防抖：检查距离上次点击是否超过3秒，因为授权信息收集完成到服务器保存成功有一段时间的间隙，这个间隙可能导致重复提交
                    const now = Date.now()
                    if (now - lastClickTimeRef.current < 3000) {
                      console.log('防抖：距离上次点击不足3秒，忽略此次点击')
                      return
                    }
                    lastClickTimeRef.current = now

                    setPending(true)
                    const message = await browserService.authorized(activeTab.id)
                    setAuthorizeErrorMessage(message)
                  } finally {
                    setPending(false)
                  }
                }}
              >
                {pending ? '请稍候...' : '我已完成登录'}
              </button>
            </div>

            {/* 功能引导弹窗 - 显示在授权条下方 */}
            {showGuide && (
              <div className="absolute top-full right-4 mt-2 pointer-events-none" style={{ zIndex: 2147483647 }}>
                <div className="relative w-max max-w-sm min-w-64 rounded-xl p-4 pointer-events-auto" style={{
                  zIndex: 2147483647,
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(255,255,255,0.92) 100%)',
                  backdropFilter: 'blur(12px)',
                  WebkitBackdropFilter: 'blur(12px)',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.4)',
                  border: '1px solid rgba(255, 255, 255, 0.4)'
                }}>
                  {/* 箭头指向上方的登录按钮 - 精美样式 */}
                  <div className="absolute -top-2 right-12">
                    {/* 箭头外层阴影 */}
                    <div className="absolute top-1 left-1 h-3 w-3 rotate-45 bg-black/15 blur-md"></div>
                    {/* 箭头内层阴影 */}
                    <div className="absolute top-0.5 left-0.5 h-3.5 w-3.5 rotate-45 bg-black/8 blur-sm"></div>
                    {/* 箭头边框 */}
                    <div className="absolute top-0 left-0 h-4 w-4 rotate-45 border-l border-t border-white/40 bg-gradient-to-br from-white/98 to-white/92"></div>
                    {/* 箭头主体 - 毛玻璃效果 */}
                    <div className="relative h-4 w-4 rotate-45 bg-white/95 backdrop-blur-md" style={{
                      background: 'linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(255,255,255,0.92) 100%)',
                      backdropFilter: 'blur(12px)',
                      WebkitBackdropFilter: 'blur(12px)'
                    }}></div>
                  </div>

                  {/* 关闭按钮 */}
                  <button
                    onClick={() => {
                      setShowGuide(false)
                    }}
                    className="absolute right-3 top-3 flex h-5 w-5 items-center justify-center rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-600 transition-colors"
                  >
                    ×
                  </button>

                  {/* 内容 */}
                  <div className="pr-6">
                    <h3 className="mb-2 text-sm font-medium text-gray-900 whitespace-nowrap">
                      如何完成账号授权？
                    </h3>
                    <p className="mb-3 text-xs text-gray-600 leading-relaxed">
                      登录页面跳转成功后，点击"我已完成登录"完成账号授权。
                    </p>

                    {/* 按钮组 */}
                    <div className="flex justify-end gap-2 flex-wrap">
                      <button
                        onClick={() => {
                          localStorage.setItem('hideAuthGuide', 'true')
                          setShowGuide(false)
                        }}
                        className="rounded-lg bg-gray-100 px-3 py-1.5 text-xs font-medium text-gray-700 hover:bg-gray-200 transition-colors whitespace-nowrap"
                      >
                        不再提示
                      </button>
                      <button
                        onClick={() => {
                          setShowGuide(false)
                        }}
                        className="rounded-lg bg-gradient-to-r from-indigo-600 to-blue-600 px-3 py-1.5 text-xs font-medium text-white shadow-lg hover:from-indigo-700 hover:to-blue-700 transition-all duration-200 whitespace-nowrap"
                      >
                        我知道了
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
        <div className="flex-grow"></div>
      </div>

      {/* 关闭确认弹窗 */}
      {showCloseConfirm && (
        <div className="fixed inset-0 z-[2147483647] flex items-center justify-center bg-black/50 backdrop-blur-sm">
          <div className="relative w-96 max-w-sm rounded-xl p-6 pointer-events-auto" style={{
            background: 'linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(255,255,255,0.92) 100%)',
            backdropFilter: 'blur(12px)',
            WebkitBackdropFilter: 'blur(12px)',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.4)',
            border: '1px solid rgba(255, 255, 255, 0.4)'
          }}>

            {/* 内容 */}
            <p className="mb-6 text-sm text-gray-600 leading-relaxed">
              未完成账号授权，确定关闭授权窗口吗？
            </p>

            {/* 按钮组 */}
            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowCloseConfirm(false)
                  window.api.send(browserChannel.confirmClose, false)
                }}
                className="rounded-lg bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 transition-colors"
              >
                取消
              </button>
              <button
                onClick={() => {
                  setShowCloseConfirm(false)
                  window.api.send(browserChannel.confirmClose, true)
                }}
                className="rounded-lg bg-gradient-to-r from-red-600 to-red-700 px-4 py-2 text-sm font-medium text-white shadow-lg hover:from-red-700 hover:to-red-800 transition-all duration-200"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
