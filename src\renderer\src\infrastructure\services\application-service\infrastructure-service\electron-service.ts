import { ImageFileInfo } from '@renderer/infrastructure/model'
import { VideoFileInfo } from '@renderer/infrastructure/model'
import { uiEvents, windowEvents } from '@common/events/ui-events'
import { mainEvents } from '@common/events/main-events'
import type { ImageFileInfoStructure } from '@common/model'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'

type MediaType = 'video' | 'image'

/**
 * @deprecated 这个类是最早和主进程相关的功能，中间的所有方法应该重构到对应的服务中，并且不应该继续往这个类增加方法
 */
class ElectronService {
  //TODO 考虑移到LocalFileService
  public async openVideoFile() {
    const paths = await window.api.invoke<string[]>(uiEvents.showOpenDialogSync, {
      properties: ['openFile'],
      filters: [{ name: 'Movies', extensions: ['mkv', 'avi', 'mp4', 'mov'] }],
    })
    if (paths.length === 0) return null
    const video = VideoFileInfo.fromObject(await window.api.invoke(uiEvents.getVideoInfo, paths[0]))
    // 缓存发布视频文件
    const filePath = await window.api.invoke<string>(uiEvents.saveVideoFile, paths[0])
    video.filePath = filePath
    return video
  }

  //TODO 考虑移到LocalFileService
  public async openVideoFiles(): Promise<VideoFileInfo[]> {
    const paths = await window.api.invoke<string[]>(uiEvents.showOpenDialogSync, {
      properties: ['openFile', 'multiSelections'],
      filters: [{ name: 'Movies', extensions: ['mkv', 'avi', 'mp4', 'mov'] }],
    })
    const result: VideoFileInfo[] = []
    for (const path of paths) {
      try {
        const video = VideoFileInfo.fromObject(await window.api.invoke(uiEvents.getVideoInfo, path))
        // 缓存发布视频文件
        const filePath = await window.api.invoke<string>(uiEvents.saveVideoFile, path)
        video.filePath = filePath
        result.push(video)
      } catch (e) {
        console.error(e)
      }
    }
    return result
  }

  openSaveDialogSync(options: Electron.SaveDialogSyncOptions) {
    return window.api.invoke<string>(uiEvents.showSaveDialogSync, options)
  }

  // 文章导入， 解析url链接
  public async parsingImportURLContent(url: string) {
    return await window.api.invoke<{ title: string | undefined; content: string | undefined }>(
      uiEvents.parsingURLContent,
      url,
    )
  }

  saveCSVFile(content: string, path: string) {
    return window.api.invoke<string>(uiEvents.saveCSVFile, content, path)
  }

  async compressImage(path: string): Promise<ImageFileInfo> {
    try {
      const buffer = await this.readFileBuffer(path)
      const qualityNumber = this.calculateQualityNumber(ByteSize.fromB(buffer.byteLength))
      const compressedBuffer = await this.bufferCompression(buffer, qualityNumber)
      const savedPath = await this.saveImageFile(compressedBuffer, 'jpg')
      return this.getImageFileInfo(savedPath)
    } catch (e) {
      console.error(`压缩图片文件时出错: ${path}`, e)
      throw e
    }
  }

  private calculateQualityNumber(byteSize: ByteSize): number {
    if (byteSize < ByteSize.fromKB(512)) return 80
    if (byteSize < ByteSize.fromMB(5)) return 50
    return 20
  }

  public async openImageFile(
    ...extensions: ('jpg' | 'png' | 'jpeg' | 'webp')[]
  ): Promise<ImageFileInfo | null> {
    const paths = await window.api.invoke<string[]>(uiEvents.showOpenDialogSync, {
      properties: ['openFile'],
      filters: [{ name: 'Images', extensions: extensions }],
    })

    if (!paths || !paths[0]) return null

    return this.getImageFileInfo(paths[0])
  }

  public async openImageFiles(
    ...extensions: ('jpg' | 'png' | 'jpeg' | 'webp')[]
  ): Promise<ImageFileInfo[]> {
    const paths = await window.api.invoke<string[]>(uiEvents.showOpenDialogSync, {
      properties: ['openFile', 'multiSelections'],
      filters: [{ name: 'Images', extensions: extensions }],
    })

    const result: ImageFileInfo[] = []
    for (const path of paths) {
      try {
        const imageInfo = await this.getImageFileInfo(path)
        result.push(imageInfo)
      } catch (e) {
        //ignore
      }
    }
    return result
  }

  //TODO 考虑移到LocalFileService
  public async openCompressedImageFile(
    ...extensions: ('jpg' | 'png' | 'jpeg' | 'webp')[]
  ): Promise<ImageFileInfo | null> {
    const paths = await window.api.invoke<string[]>(uiEvents.showOpenDialogSync, {
      properties: ['openFile'],
      filters: [{ name: 'Images', extensions: extensions }],
    })

    if (!paths || !paths[0]) return null

    return this.compressImage(paths[0])
  }

  public async openCompressedImageFiles(
    ...extensions: ('jpg' | 'png' | 'jpeg' | 'webp')[]
  ): Promise<ImageFileInfo[]> {
    const paths = await window.api.invoke<string[]>(uiEvents.showOpenDialogSync, {
      properties: ['openFile', 'multiSelections'],
      filters: [{ name: 'Images', extensions: extensions }],
    })

    const result: ImageFileInfo[] = []
    for (const path of paths) {
      try {
        const imageInfo = await this.compressImage(path)
        result.push(imageInfo)
      } catch (e) {
        //ignore
      }
    }
    return result
  }

  //TODO 考虑移到LocalFileService
  saveImageFile(arrayBuffer: ArrayBuffer, extension: string) {
    return window.api.invoke<string>(uiEvents.saveImageFile, arrayBuffer, extension)
  }

  async getImageFileInfo(path: string) {
    const object = await window.api.invoke<ImageFileInfoStructure>(uiEvents.getImageInfo, path)
    return ImageFileInfo.of(object.width, object.height, object.size, object.format, object.path)
  }

  copyTextToClipboard(text: string) {
    return window.api.invoke<void>(uiEvents.copyToClipboard, text)
  }

  //TODO 考虑移到LocalFileService
  readFileBuffer(filePath: string) {
    return window.api.invoke<Uint8Array>(uiEvents.readFileBuffer, filePath)
  }

  getAppVersion() {
    return window.api.invoke<string>(mainEvents.getAppVersion)
  }

  getDeviceId() {
    return window.api.invoke<string>(mainEvents.getDeviceId)
  }

  //TODO 考虑移到MediaService
  bufferCompression(buffer: ArrayBuffer, qualityNumber = 80) {
    return window.api.invoke<Buffer>(uiEvents.bufferCompression, buffer, qualityNumber)
  }

  public async downloadFileLocal(url: string, type: MediaType): Promise<string> {
    try {
      return window.api.invoke(uiEvents.downloadFile, url, type)
    } catch (e) {
      throw new Error(`发布时出现了网络异常，请重新发布。`)
    }
  }

  focusWindow() {
    return window.api.invoke(windowEvents.focusWindow)
  }
}

/**
 * @deprecated 这个类是最早和主进程相关的功能，中间的所有方法应该重构到对应的服务中，并且不应该继续往这个类增加方法
 */
export const electronService = new ElectronService()
