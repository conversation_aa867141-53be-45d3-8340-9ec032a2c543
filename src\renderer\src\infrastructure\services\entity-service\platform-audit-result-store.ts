import { PlatformAuditResult } from '@renderer/infrastructure/model/pushing-task/platform-audit-result'
import type { PlatformAuditResultStoreObject } from '@renderer/infrastructure/entity'
import type { PlatformAuditResultRepository } from '@renderer/infrastructure/repository/repositories'
import { usePlatformAuditResultRepository } from '@renderer/infrastructure/repository/repositories'
import { useMemo } from 'react'

export function usePlatformAuditResultStore() {
  const platformAuditResultRepository = usePlatformAuditResultRepository()
  return useMemo(
    () => new PlatformAuditResultStore(platformAuditResultRepository),
    [platformAuditResultRepository],
  )
}

export class PlatformAuditResultStore {
  constructor(private platformAuditResultRepository: PlatformAuditResultRepository) {}

  private _Model2Entity(platformAuditResult: PlatformAuditResult) {
    return {
      taskId: platformAuditResult.taskId,
      state: platformAuditResult.state,
      documentId: platformAuditResult.documentId,
      openUrl: platformAuditResult.openUrl,
      message: platformAuditResult.message,
    } satisfies PlatformAuditResultStoreObject as PlatformAuditResultStoreObject
  }

  private _Object2Model(entity: PlatformAuditResultStoreObject): PlatformAuditResult {
    return new PlatformAuditResult(
      entity.taskId,
      entity.state,
      entity.documentId,
      entity.openUrl,
      entity.message,
    )
  }

  async save(platformAuditResult: PlatformAuditResult) {
    await this.platformAuditResultRepository.save(this._Model2Entity(platformAuditResult))
  }

  async get(taskId: string) {
    const object = await this.platformAuditResultRepository.get(taskId)
    return this._Object2Model(object)
  }

  async getMany(taskIds: string[]) {
    const storeObjects = await this.platformAuditResultRepository.getAsManyAsPossible(taskIds)
    return storeObjects.map(this._Object2Model)
  }

  async deleteMany(taskIds: string[]) {
    await this.platformAuditResultRepository.bulkDelete(taskIds)
  }
}
