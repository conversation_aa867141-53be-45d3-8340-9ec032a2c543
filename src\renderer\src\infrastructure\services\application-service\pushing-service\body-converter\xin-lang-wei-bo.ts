import {
  platforms,
  type Account,
  type ImageFileInfo,
  type VideoContentViewModel,
  type VideoFileInfo,
} from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'

import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import { ignoreException } from './pushing-task-body-converter'
import { utils } from '@coozf/editor'
import { datetimeService, xinLangWeiBoPlatformService } from '@renderer/infrastructure/services'
import type { AccountSession, PlatformDataItem, WithLocalStorage } from '@common/structure'
import type {
  XinlangweiboArticleTaskInput,
  XinlangweiboLocationInfo,
  XinlangweiboMicroArticleTaskInput,
  XinlangweiboVideoTaskInput,
} from '@yixiaoer/platform-service/dist/media-platform/xinlangweibo'
import type {
  LocalArticlePushingConfig,
  LocalImageTextPushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'

class XinLangWeiBoPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    // 转换话题
    description = await this.getDescription(description, session)

    // 获取地理位置
    const location = await this.getLocation(
      locationKeyword,
      pushingConfig.location,
      account,
      session,
    )

    return {
      localStorage: session.localStorage,
      taskId: taskId,
      video: {
        duration: video.fileDurationTimeSpan.seconds,
        width: video.videoWidth,
        height: video.videoHeight,
        size: video.fileSize,
        localPath: video.filePath,
      },
      cover: {
        width: cover.width,
        height: cover.height,
        size: cover.size,
        pathOrUrl: cover.path,
      },
      title: title,
      allowDownload: undefined,
      subCategoryId: undefined,
      createType: isOriginal ? 0 : 1,
      collections: [],
      topics: [],
      desc: description,
      location: location?.raw,
      visibilityType: 0,
      publishTime: timing,
    } satisfies XinlangweiboVideoTaskInput & WithLocalStorage
  }

  async toDynamic(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalImageTextPushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    // 转换话题
    const description = await this.getDescription(config.description, session)
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      images: config.images.map((image) => ({
        width: image.width,
        height: image.height,
        size: image.size,
        pathOrUrl: image.path,
      })),
      contentText: description,
      topics: [],
      visible: toDraft ? 1 : 0,
      schedule_timestamp: config.timing,
    } satisfies XinlangweiboMicroArticleTaskInput & WithLocalStorage
  }

  async toArticle(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalArticlePushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    return {
      localStorage: session.localStorage,
      taskId: taskId,

      pubType: toDraft ? 0 : 1,
      uid: Number.parseInt(account.authorId),
      markpos: 1,
      nick: '',
      title: config.title,
      content: config.content,
      // @ts-ignore - 2025年4月2日，更新爬虫后应该去除这个ignore强制检查
      cover: {
        width: config.cover!.width,
        height: config.cover!.height,
        size: config.cover!.size,
        pathOrUrl: config.cover!.path,
      },
      follow_to_read: 0,
      summary: undefined,
      /**
       * 头条文章专栏列表，目前只支持选择一个合集
       */
      collection: undefined,
      article_recommend: undefined,
      mblog_statement: undefined,
      /**
       *可见类型 10：粉丝 0：公开
       */
      rank: 0,
      /**
       * 定时发布时间 yyyy-MM-dd HH:mm
       */
      time: config.timing === null ? '' : datetimeService.format(config.timing, 'yyyy-MM-dd HH:mm'),
      /**
       * 微博内容描述，当不给值时，默认按照官方规则使用文章标题拼接
       */
      contentText: '',
      /**
       * 微博话题
       */
      topics: [],
      /**
       * 1@微博好友列表
       */
      mentionedFriends: [],
    } satisfies XinlangweiboArticleTaskInput & WithLocalStorage
  }

  private async getDescription(description: string, session: AccountSession) {
    return await utils.commonTopic.modifyTopics(description, async (element) => {
      await ignoreException(async () => {
        const keyword = element.getAttribute('text')
        if (!keyword) return

        const topic =
          (await xinLangWeiBoPlatformService.getTopics(session, keyword))[0] ?? undefined
        if (!topic) element.remove()
        else element.setAttribute('raw', JSON.stringify(topic.raw))
      })
    })
  }

  private async getLocation(
    locationKeyword: string,
    configLocation: VideoContentViewModel['location'] | null,
    account: Account,
    session: AccountSession,
  ) {
    if (configLocation && configLocation[platforms.XinLangWeiBo.name]) {
      return configLocation[
        platforms.XinLangWeiBo.name
      ] as PlatformDataItem<XinlangweiboLocationInfo>
    }
    let location: PlatformDataItem<XinlangweiboLocationInfo> | undefined
    await ignoreException(async () => {
      location = locationKeyword
        ? (await xinLangWeiBoPlatformService.getLocations(account, session, locationKeyword))[0] ??
          undefined
        : undefined
    })
    return location
  }
}

export const xinLangWeiBo = new XinLangWeiBoPushingTaskBodyConverter()
