import { zodResolver } from '@hookform/resolvers/zod'
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { LoadingButton } from '@renderer/components/LoadingButton'
import { VerificationCodeButton } from '@renderer/components/verificationCodeButton'
import { useApiMutation } from '@renderer/hooks/useApiQuery'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@renderer/shadcn-components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@renderer/shadcn-components/ui/form'
import { Input } from '@renderer/shadcn-components/ui/input'
import { zodIsPhone } from '@renderer/utils/zod'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { toast } from 'sonner'

// 账号验证规则：8-20位字符串
const accountSchema = z
  .string()
  .min(8, { message: '账号至少8位字符' })
  .max(20, { message: '账号最多20位字符' })
  .regex(/^[a-zA-Z0-9]+$/, { message: '账号只能包含字母、数字' })

const formSchema = z.object({
  phone: zodIsPhone,
  code: z.string().length(6, { message: '验证码必须为6位' }),
  account: accountSchema,
})

type FormSchema = z.infer<typeof formSchema>

interface BindAccountProps {
  hasPhone: boolean
  currentPhone?: string
  onSuccess?: () => void
  children?: React.ReactNode
}

export const BindAccount = ({ hasPhone, currentPhone, onSuccess, children }: BindAccountProps) => {
  const title = '绑定账号'

  const [open, setOpen] = useState(false)

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      phone: currentPhone || '',
      code: '',
      account: '',
    },
    mode: 'onChange',
  })

  // 账号绑定的API调用
  const bindMutation = useApiMutation<{ phone: string; code: string; account: string }, void>(
    (params) => ({
      url: '/users/bind/account',
      method: 'PUT',
      data: params,
    }),
    {
      onSuccess: () => {
        setOpen(false)
        toast.success('账号绑定成功')
        onSuccess?.()
      },
      onError: (error) => {
        console.error('账号绑定失败:', error)
      },
    },
  )

  const onSubmit = (data: FormSchema) => {
    bindMutation.mutate({
      phone: data.phone,
      code: data.code,
      account: data.account,
    })
  }

  const onOpenChange = (open: boolean) => {
    setOpen(open)
    if (!open) {
      form.reset()
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        {children || <Button variant="outline">绑定账号</Button>}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <VisuallyHidden>
            <DialogDescription>绑定您的账号以增强安全性</DialogDescription>
          </VisuallyHidden>
        </DialogHeader>

        {!hasPhone ? (
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              为了保障您的账号安全，请先绑定手机号码。
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setOpen(false)}>
                取消
              </Button>
            </div>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>手机号</FormLabel>
                    <FormControl>
                      <Input
                        disabled={hasPhone}
                        placeholder="请输入手机号"
                        maxLength={11}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>验证码</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          maxLength={6}
                          className="pr-24"
                          placeholder="请输入验证码"
                          {...field}
                        />
                        <VerificationCodeButton
                          sence="bindAccount"
                          className="right-1 top-1 h-7"
                          phoneName="phone"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="account"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>账号</FormLabel>
                    <FormControl>
                      <Input placeholder="请输入账号（8-20位字符）" maxLength={20} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                  取消
                </Button>
                <LoadingButton
                  isPending={bindMutation.isPending}
                  disabled={!form.formState.isValid || bindMutation.isPending}
                  type="submit"
                >
                  绑定
                </LoadingButton>
              </div>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  )
}
