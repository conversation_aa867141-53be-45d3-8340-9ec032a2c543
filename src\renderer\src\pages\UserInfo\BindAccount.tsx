import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@renderer/shadcn-components/ui/dialog'
import { useState } from 'react'
import { BindPhone } from './BindPhone'
import { ChangePhone } from './changePhone'

interface BindAccountProps {
  hasPhone: boolean
  currentPhone?: string
  onSuccess?: () => void
  children?: React.ReactNode
}

export const BindAccount = ({ hasPhone, currentPhone, onSuccess, children }: BindAccountProps) => {
  const [open, setOpen] = useState(false)
  const [showPhoneBind, setShowPhoneBind] = useState(false)

  const handlePhoneBindSuccess = () => {
    setShowPhoneBind(false)
    setOpen(false)
    onSuccess?.()
  }

  const onOpenChange = (open: boolean) => {
    setOpen(open)
    if (!open) {
      setShowPhoneBind(false)
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogTrigger asChild>
          {children || <Button variant="outline">绑定账号</Button>}
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>绑定账号</DialogTitle>
            <VisuallyHidden>
              <DialogDescription>绑定您的账号以增强安全性</DialogDescription>
            </VisuallyHidden>
          </DialogHeader>

          <div className="space-y-4">
            {hasPhone && currentPhone ? (
              <>
                <div className="text-sm text-muted-foreground">
                  您的账号已绑定手机号码，可以进行管理操作。
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between rounded-lg border p-4">
                    <div>
                      <div className="font-medium">当前手机号</div>
                      <div className="text-sm text-muted-foreground">{currentPhone}</div>
                    </div>
                    <ChangePhone
                      phone={currentPhone}
                      onSuccess={() => {
                        setOpen(false)
                        onSuccess?.()
                      }}
                    />
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="text-sm text-muted-foreground">
                  为了保障您的账号安全，请绑定您的手机号码。
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between rounded-lg border p-4">
                    <div>
                      <div className="font-medium">手机号码</div>
                      <div className="text-sm text-muted-foreground">未绑定</div>
                    </div>
                    <Button variant="outline" size="sm" onClick={() => setShowPhoneBind(true)}>
                      绑定手机号码
                    </Button>
                  </div>
                </div>
              </>
            )}

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setOpen(false)}>
                取消
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <BindPhone
        onSuccess={handlePhoneBindSuccess}
        open={showPhoneBind}
        onOpenChange={setShowPhoneBind}
      />
    </>
  )
}
