import {
  type PlatformAuditResultStore,
  type PushingTaskPushResultStore,
  type PushingTaskStore,
  usePlatformAuditResultStore,
  usePushingTaskPushResultStore,
  usePushingTaskStore,
} from '@renderer/infrastructure/services/entity-service'
import {
  type PushingTaskAccountStore,
  usePushingTaskAccountStore,
} from '@renderer/infrastructure/services/entity-service/pushing-task-account-store'
import { useMemo } from 'react'
import type {
  PushingTaskModelAggregate,
  PushingTaskSetIdentifier,
} from '@renderer/infrastructure/model'
import type { PublishStore } from '../../entity-service/publish-store'
import { usePublishStore } from '../../entity-service/publish-store'

/**
 * 本地推送任务聚合服务，这个服务用于对外提供完整的推送任务聚合，只依赖数据库
 */
export function useLocalPushingTaskAggregateService() {
  const publishStore = usePublishStore()
  const pushingTaskStore = usePushingTaskStore()
  const pushingTaskAccountStore = usePushingTaskAccountStore()
  const pushingTaskPushResultStore = usePushingTaskPushResultStore()
  const platformAuditResultStore = usePlatformAuditResultStore()
  return useMemo(
    () =>
      new LocalPushingTaskAggregateService(
        publishStore,
        pushingTaskStore,
        pushingTaskAccountStore,
        pushingTaskPushResultStore,
        platformAuditResultStore,
      ),
    [
      publishStore,
      pushingTaskStore,
      pushingTaskAccountStore,
      pushingTaskPushResultStore,
      platformAuditResultStore,
    ],
  )
}

export class LocalPushingTaskAggregateService {
  constructor(
    private publishStore: PublishStore,
    private pushingTaskStore: PushingTaskStore,
    private pushingTaskAccountStore: PushingTaskAccountStore,
    private pushingTaskPushResultStore: PushingTaskPushResultStore,
    private platformAuditResultStore: PlatformAuditResultStore,
  ) {}

  async getTaskAggregate(
    taskSetId: PushingTaskSetIdentifier,
  ): Promise<PushingTaskModelAggregate[]> {
    const taskIds = await this.publishStore.getTaskIds(taskSetId)

    const tasks = await this.pushingTaskStore.getMany(taskIds)
    const pushResults = await this.pushingTaskPushResultStore.getAll(taskIds)
    const auditResults = await this.platformAuditResultStore.getMany(taskIds)
    const accounts = await this.pushingTaskAccountStore.getMany(taskIds)

    return tasks.map((task) => {
      return {
        task,
        account: accounts.find((x) => x.taskId === task.taskId) ?? null,
        pushResult: pushResults.find((x) => x.taskId === task.taskId)!,
        auditResult: auditResults.find((x) => x.taskId === task.taskId) ?? null,
        progress: 0,
        queryState: null,
      } satisfies PushingTaskModelAggregate as PushingTaskModelAggregate
    })
  }

  async getPushResult(taskId: string) {
    return this.pushingTaskPushResultStore.get(taskId)
  }

  async getAuditResult(taskId: string) {
    return this.platformAuditResultStore.get(taskId)
  }
}
