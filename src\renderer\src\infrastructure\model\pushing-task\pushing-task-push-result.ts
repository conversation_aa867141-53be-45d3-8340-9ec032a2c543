export type PushState = '视频上传中' | '等待推送' | '正在推送' | '推送成功' | '推送失败'

export class PushingTaskPushResult {
  constructor(
    public taskId: string,
    public state: PushState = '等待推送',
    public message: string = '',
    public publishId: string | null = null,
  ) {}

  updateProgress(message: string) {
    if (this.finished) return
    this.state = '正在推送'
    this.message = message
  }

  get finished() {
    return this.state === '推送成功' || this.state === '推送失败'
  }

  reset() {
    this.state = '等待推送'
    this.message = ''
  }

  succeed(publishId: string) {
    this.state = '推送成功'
    this.publishId = publishId
  }

  fail(message: string) {
    this.state = '推送失败'
    this.message = message
  }

  videoUploading() {
    this.state = '视频上传中'
    this.message = ''
  }
}
