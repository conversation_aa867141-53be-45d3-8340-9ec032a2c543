import type { PushState } from '../model/pushing-task'

export interface PushingTaskPushResultStoreObject {
  taskId: string
  state: PushState
  message: string
  publishId: string | null
}

export class PushingTaskPushResultEntity implements PushingTaskPushResultStoreObject {
  constructor(
    public taskId: string,
    public state: PushState,
    public message: string,
    public publishId: string | null,
  ) {}
}
