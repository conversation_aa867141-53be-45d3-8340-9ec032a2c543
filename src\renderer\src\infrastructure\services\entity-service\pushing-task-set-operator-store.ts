import type { PushingTaskSetOperatorRepository } from '../../repository/repositories'
import { usePushingTaskSetOperatorRepository } from '../../repository/repositories'
import type { PushingTaskSetOperatorStoreObject } from '../../entity'
import { useMemo } from 'react'
import { PushingTaskSetOperator } from '@renderer/infrastructure/model/pushing-task/pushing-task-set-operator'
import type { PushingTaskSetIdentifier } from '@renderer/infrastructure/model'

export function usePushingTaskSetOperatorStore() {
  const repository = usePushingTaskSetOperatorRepository()
  return useMemo(() => new PushingTaskSetOperatorStore(repository), [repository])
}

function _Object2Model(entity: PushingTaskSetOperatorStoreObject): PushingTaskSetOperator {
  return new PushingTaskSetOperator(entity.taskSetId, entity.operatorId, entity.operatorName)
}

function _Model2Object(entity: PushingTaskSetOperator): {
  taskSetId: string
  operatorId: string
  operatorName: string
} {
  return {
    taskSetId: entity.taskSetId,
    operatorId: entity.operatorId,
    operatorName: entity.operatorName,
  } satisfies PushingTaskSetOperatorStoreObject
}

export class PushingTaskSetOperatorStore {
  constructor(private repository: PushingTaskSetOperatorRepository) {}

  async save(model: PushingTaskSetOperator) {
    await this.repository.save(_Model2Object(model))
  }

  async getMany(taskIds: PushingTaskSetIdentifier[]) {
    return (
      await this.repository.getMany((x) => x.where('taskSetId').anyOf(taskIds).toArray())
    ).map(_Object2Model)
  }

  // del
  async delete(taskSetId: string | string[]) {
    if (Array.isArray(taskSetId)) {
      return await this.repository.bulkDelete(taskSetId)
    } else {
      return await this.repository.delete(taskSetId)
    }
  }
}
