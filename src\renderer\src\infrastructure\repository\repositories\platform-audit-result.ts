import { Repository } from '@renderer/infrastructure/repository'
import type { PlatformAuditResultStoreObject } from '@renderer/infrastructure/entity'
import type { MemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemo } from 'react'

export function usePlatformAuditResultRepository() {
  const database = useMemberDatabase()
  return useMemo(() => new PlatformAuditResultRepository(database), [database])
}

export class PlatformAuditResultRepository extends Repository<
  PlatformAuditResultStoreObject,
  'taskId'
> {
  constructor(database: MemberDatabase) {
    super(database.instance.PlatformAuditResult)
  }
}
