import type { EditContentType } from '@common/model/content-type'
import type {
  Platform,
  PushingTaskSet,
  PushingTaskSetIdentifier,
  PushingTaskSetState,
} from '@renderer/infrastructure/model'

import type { PushingTaskSetOperator } from '@renderer/infrastructure/model/pushing-task/pushing-task-set-operator'

export type PushingTaskSetViewModel = {
  taskSetId: PushingTaskSetIdentifier
  brief: string
  thumbUrl: string
  state: PushingTaskSetState
  platforms: Platform[]
  contentType: EditContentType
  createTime: Date
  operatorId: string
  operatorName: string
  isFromApp: boolean
  isDraft: boolean
  isCloudTaskSet: boolean
}

export type PushingTaskSetAggregate = {
  taskSet: PushingTaskSet
  operator: PushingTaskSetOperator
}
