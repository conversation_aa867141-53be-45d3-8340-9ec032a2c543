import { type VideoSpecification } from '../video-specification'
import { defaultSpecification } from '../default'
import { ByteSize } from '@renderer/infrastructure/model/utils/byte-size'
import { TimeSpan } from '@renderer/infrastructure/model/utils/time-span'

export default {
  ...defaultSpecification,
  videoMaxByteSize: ByteSize.fromGB(16),
  videoMaxDuration: TimeSpan.fromMinutes(60),
  titleSupport: true,
  titleMaxLength: 30,
  descriptionMaxLength: 1000,
  topicMaxCount: 5,
  topicMaxLength: 500,
  scheduledTimeSupport: true,
  scheduledTimeMinTimeSpan: TimeSpan.fromHours(2),
  scheduledTimeMaxTimeSpan: TimeSpan.fromDays(14),
  locationSupport: true,
  privatePublishSupport: true,
} satisfies VideoSpecification as VideoSpecification
