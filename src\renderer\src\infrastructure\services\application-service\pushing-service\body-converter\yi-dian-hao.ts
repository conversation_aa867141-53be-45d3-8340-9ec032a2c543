import type { Account, ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import type { AccountSession, WithLocalStorage } from '@common/structure'
import type {
  LocalArticlePushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { IYiDianHaoArticleData } from '@yixiaoer/platform-service/dist/media-platform/yidianhao/yidianhao-article'
import { platformNames } from '@common/model/platform-name'
import type { YidianhaoVideoTaskInput } from '@yixiaoer/platform-service'

class YiDianHaoPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    const platformConfig = pushingConfig.platformConfigs[platformNames.YiDianHao]
    const categories = platformConfig.categories.map((x) => x.raw)

    if (!categories) {
      throw new Error('一点号分类不能为空')
    }

    const tags = pushingConfig.tags

    return {
      localStorage: session.localStorage,
      taskId: taskId,
      video: {
        duration: pushingConfig.video!.fileDurationTimeSpan.seconds,
        width: pushingConfig.video!.videoWidth,
        height: pushingConfig.video!.videoHeight,
        size: pushingConfig.video!.fileSize,
        localPath: pushingConfig.video!.filePath,
      },
      cover: {
        width: pushingConfig.cover!.width,
        height: pushingConfig.cover!.height,
        size: pushingConfig.cover!.size,
        pathOrUrl: pushingConfig.cover!.path,
      },
      desc: description,
      title: title,

      categoryName: categories[0].text,
      subCategoryName: categories[1].text,

      /**
       * 标签
       */
      tags: tags,
      /**
       * 1 无需申明, 2 自行拍摄, 3 内容取材网络 ， 4 内容由AI生成 ,5 虚构情节内容
       */
      wm_content_source: undefined,
      /**
       * 创作类型 1:原创 0:转载
       */
      isOriginal: pushingConfig.isOriginal ? 1 : 0,
      /**
       * 定时发布13位时间戳
       */
      publishTime: pushingConfig.timing,

      pubType: toDraft ? 0 : 1,
    } satisfies YidianhaoVideoTaskInput & WithLocalStorage
  }

  async toArticle(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalArticlePushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      title: config.title,
      covers: [
        {
          width: config.cover!.width,
          height: config.cover!.height,
          size: config.cover!.size,
          pathOrUrl: config.cover!.path,
        },
      ],
      pubType: toDraft ? 0 : 1,
      content: config.content,
      //@ts-ignore 等待类型修复为可空
      cancelToken: undefined,
      prePubTime: config.timing ?? 0,
    } satisfies IYiDianHaoArticleData & WithLocalStorage
  }
}

export const yiDianHao = new YiDianHaoPushingTaskBodyConverter()
