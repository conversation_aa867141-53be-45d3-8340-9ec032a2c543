import type { VideoPushingConfigRepository } from '@renderer/infrastructure/repository/repositories/pushing-config'
import { useVideoPushingConfigRepository } from '@renderer/infrastructure/repository/repositories/pushing-config'
import type { AllPushingConfig } from '@renderer/infrastructure/model/config/pushing-config'
import {
  CloudVideoPushingConfig,
  LocalArticlePushingConfig,
  LocalImageTextPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import { LocalVideoPushingConfig } from '@renderer/infrastructure/model/config/pushing-config'
import type { VideoPlatformConfigs } from '@renderer/infrastructure/model'
import {
  getPlatformByName,
  getPushContentTypeByName,
  ImageFileInfo,
  newVideoPlatformConfigs,
  VideoFileInfo,
} from '@renderer/infrastructure/model'
import { useMemo } from 'react'
import type { VideoPushingConfigStoreObject } from '@renderer/infrastructure/entity'
import type { TimeStamp } from '@renderer/infrastructure/types/brand'
import type { ArticlePlatformConfigs } from '@renderer/infrastructure/model/config/article-platforms'
import { newArticlePlatformConfigs } from '@renderer/infrastructure/model/config/article-platforms'

function _VideoPushingConfigModel2Entity(config: AllPushingConfig): VideoPushingConfigStoreObject {
  if (config instanceof LocalVideoPushingConfig) {
    return {
      type: 'local',
      configId: config.configId,
      accountId: config.accountId,
      platformName: config.platform.name,
      contentTypeName: config.pushContentType,
      title: config.title,
      description: config.description,
      isOriginal: config.isOriginal,
      locationKeyword: config.locationKeyword,
      location: config.location,
      video: config.video,
      cover: config.cover,
      timing: config.timing,
      tags: config.tags,
      toDraft: config.toDraft,
      platformConfigs: config.platformConfigs,
    } satisfies VideoPushingConfigStoreObject
  } else if (config instanceof CloudVideoPushingConfig) {
    return {
      type: 'cloud',
      configId: config.configId,
      accountId: config.accountId,
      platformName: config.platform.name,
      contentTypeName: config.pushContentType,
      title: config.title,
      description: config.description,
      isOriginal: config.isOriginal,
      locationKeyword: config.locationKeyword,
      location: null,
      cover: config.cover,
      videoUrl: config.videoUrl,
      coverUrl: config.coverUrl,
      token: config.token,
      appTaskId: config.appTaskId,
      timing: config.timing,
    } satisfies VideoPushingConfigStoreObject
  } else if (config instanceof LocalImageTextPushingConfig) {
    return {
      type: 'dynamic',
      configId: config.configId,
      accountId: config.accountId,
      platformName: config.platform.name,
      contentTypeName: config.pushContentType,
      title: config.title,
      description: config.description,
      isOriginal: true,
      locationKeyword: config.locationKeyword,
      location: config.location,
      cover: config.cover,
      music: config.music,
      images: config.images,
      timing: config.timing,
    } satisfies VideoPushingConfigStoreObject
  } else if (config instanceof LocalArticlePushingConfig) {
    return {
      type: 'article',
      configId: config.configId,
      accountId: config.accountId,
      platformName: config.platform.name,
      contentTypeName: config.pushContentType,
      title: config.title,
      content: config.content,
      isFirst: config.isFirst,
      locationKeyword: config.locationKeyword,
      location: null,
      cover: config.cover,
      verticalCover: config.verticalCover ?? undefined,
      category: config.category,
      toDraft: config.toDraft,
      timing: config.timing ?? undefined,
      platformConfigs: config.platformConfigs,
    } satisfies VideoPushingConfigStoreObject
  }
  throw Error('未知的类型:')
}

function _VideoPushingConfigObject2Model(object: VideoPushingConfigStoreObject) {
  if (!object.type || object.type === 'local') {
    return new LocalVideoPushingConfig(
      object.configId,
      object.accountId,
      getPlatformByName(object.platformName),
      object.locationKeyword,
      object.location,
      object.isOriginal!,
      getPushContentTypeByName(object.contentTypeName),
      object.title,
      object.description!,
      VideoFileInfo.fromObject(object.video!),
      ImageFileInfo.fromObject(object.cover!),
      object.tags ?? [],
      object.toDraft ?? false,
      object.timing as TimeStamp,
      (object.platformConfigs as VideoPlatformConfigs) ?? newVideoPlatformConfigs(),
    )
  } else if (object.type === 'cloud') {
    return new CloudVideoPushingConfig(
      object.configId,
      object.accountId,
      getPlatformByName(object.platformName),
      object.token as '',
      object.appTaskId as '',
      object.videoUrl as string,
      object.coverUrl as string,
      getPushContentTypeByName(object.contentTypeName),
      object.title,
      object.description!,
      object.isOriginal!,
      object.locationKeyword,
      ImageFileInfo.fromObject(object.cover!),
      object.timing as TimeStamp,
    )
  } else if (!object || object.type === 'dynamic') {
    return new LocalImageTextPushingConfig(
      object.configId,
      object.accountId,
      getPlatformByName(object.platformName),
      object.locationKeyword,
      object.location,
      getPushContentTypeByName(object.contentTypeName),
      object.title,
      object.description!,
      object.music!,
      object.images!.map((x) => ImageFileInfo.fromObject(x)),
      ImageFileInfo.fromObject(object.cover!),
      object.timing as TimeStamp,
    )
  } else if (object.type === 'article') {
    return new LocalArticlePushingConfig(
      object.configId,
      object.accountId,
      getPlatformByName(object.platformName),
      getPushContentTypeByName(object.contentTypeName),
      object.title,
      object.cover ? ImageFileInfo.fromObject(object.cover) : null,
      object.content!,
      object.isFirst!,
      object.category!,
      object.verticalCover ? ImageFileInfo.fromObject(object.verticalCover) : null,
      object.locationKeyword,
      (object.timing as TimeStamp) ?? null,
      !!object.toDraft,
      (object.platformConfigs as ArticlePlatformConfigs) ?? newArticlePlatformConfigs(),
    )
  } else {
    throw Error('未知的类型:' + object.type)
  }
}

export function usePushingConfigStore() {
  const repository = useVideoPushingConfigRepository()
  return useMemo(() => new PushingConfigStore(repository), [repository])
}

export class PushingConfigStore {
  constructor(private pushingConfigRepository: VideoPushingConfigRepository) {}

  async saveConfigs(configs: AllPushingConfig[]) {
    await this.pushingConfigRepository.bulkSave(configs.map(_VideoPushingConfigModel2Entity))
  }

  async getConfig(configId: string) {
    const object = await this.pushingConfigRepository.get(configId)
    return _VideoPushingConfigObject2Model(object)
  }

  async getConfigs(configIds: string[]) {
    const objects = await this.pushingConfigRepository.getAll(configIds)
    return objects.map(_VideoPushingConfigObject2Model)
  }

  async deleteConfigs(configIds: string[]) {
    await this.pushingConfigRepository.bulkDelete(configIds)
  }
}
