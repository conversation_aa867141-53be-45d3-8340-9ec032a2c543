import { useEffect } from 'react'
import { useAuthorizeService, usePushingService } from '@renderer/infrastructure/services'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { pushEvents } from '@renderer/infrastructure/event-bus/business-events'
import { uiEvents } from '@common/events/ui-events'
import type { PushingError } from '@common/structure'
import { publishChannels } from '@common/events/publish-chennels'
import type {
  PushingTaskSetIdentifier,
  WechatShiPinHao3rdPartySubAccount,
} from '@renderer/infrastructure/model'
import { useWechat3rdPartyService } from '@renderer/infrastructure/services/application-service/account/wechat-3rd-party-service'

export function usePushingHandler() {
  const {
    updateCloudPushResult,
    updatePushingTaskSetState,
    auditStateQuery,
    succeedTask,
    updateProgress,
    failTask,
  } = usePushingService()

  const {
    unlockWechatAccount,
    keepTokenAlive,
    getAccountSessionWithToken,
    tryUnlockWechatAccount,
  } = useWechat3rdPartyService()

  const authorizeService = useAuthorizeService()

  /**
   * 上报云端推送结果变更
   */
  useEffect(() => {
    return eventBus.on(pushEvents.pushResultChanged, async (taskId: string) => {
      await updateCloudPushResult(taskId)
    })
  }, [updateCloudPushResult])

  /**
   * 更新本地任务集
   */
  useEffect(() => {
    return eventBus.on(
      pushEvents.pushTaskCompleted,
      async ({
        taskId,
        wechatLockToken,
      }: {
        taskId: string
        wechatLockToken: [string, string] | null
      }) => {
        await updatePushingTaskSetState(taskId)
        await tryUnlockWechatAccount(taskId, wechatLockToken)
      },
    )
  }, [tryUnlockWechatAccount, updatePushingTaskSetState])

  useEffect(() => {
    return eventBus.on(pushEvents.auditResultUpdated, async (taskId: string) => {
      await updatePushingTaskSetState(taskId)
    })
  }, [updatePushingTaskSetState])

  useEffect(() => {
    return window.api.on(
      uiEvents.stateQueryStart,
      async (_event: Electron.IpcRendererEvent, taskSetId: PushingTaskSetIdentifier) => {
        console.debug('开始查询作品状态：', taskSetId)
        await auditStateQuery(taskSetId)
      },
    )
  }, [auditStateQuery])

  useEffect(() => {
    return window.api.on(
      uiEvents.pushVideoSuccess,
      async (
        _event: Electron.IpcRendererEvent,
        platformName: string,
        accountId: string,
        taskId: string,
        publishId: string,
        wechatLockToken: [string, string] | null,
      ) => {
        await succeedTask(taskId, publishId, wechatLockToken)
        console.debug('推送成功：', taskId)
      },
    )
  }, [succeedTask])

  useEffect(() => {
    return window.api.on(
      uiEvents.pushVideoProgress,
      async (
        _event: Electron.IpcRendererEvent,
        taskId: string,
        progress: number,
        message: string,
      ) => {
        await updateProgress(taskId, progress, message)
      },
    )
  }, [updateProgress])

  useEffect(() => {
    return window.api.on(
      uiEvents.pushVideoFailed,
      async (
        _event: Electron.IpcRendererEvent,
        platformName: string,
        accountId: string,
        taskId: string,
        error: PushingError,
        wechatLockToken: [string, string] | null,
      ) => {
        await failTask(taskId, error.message, wechatLockToken)
        console.error('推送失败：', taskId, error)
      },
    )
  }, [failTask])

  useEffect(() => {
    return window.api.on(
      publishChannels.getAccountSession,
      async (_event, requestId, accountId, wechatToken) => {
        try {
          const account = (await authorizeService.getSpiderAccount(
            accountId,
          )) as WechatShiPinHao3rdPartySubAccount //目前为止只有微信第三方账号采用了这种方式获取Session
          const session = await getAccountSessionWithToken(account, wechatToken)
          window.api.send(publishChannels.getAccountSessionReply, requestId, session)
        } catch (e) {
          console.debug('主进程获取账号会话失败', e)
          window.api.send(publishChannels.getAccountSessionFailed, requestId)
        }
      },
    )
  }, [authorizeService, getAccountSessionWithToken])

  useEffect(() => {
    return window.api.on(publishChannels.wechatKeepAlive, async (_event, accountId, token) => {
      void keepTokenAlive(accountId, token)
    })
  })

  useEffect(() => {
    return window.api.on(publishChannels.wechatRelease, async (_event, accountId, token) => {
      void unlockWechatAccount(accountId, token)
    })
  })
}
