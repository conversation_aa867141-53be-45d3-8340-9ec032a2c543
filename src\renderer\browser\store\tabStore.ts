import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { BrowserActiveTabData, BrowserTabListItem } from '@common/structure/browser/header'

interface TabState {
  tabs: BrowserTabListItem[]
  setTabs: (tabs: BrowserTabListItem[]) => void
  activeTab: BrowserActiveTabData | null
  setActiveTab: (activeTab: BrowserActiveTabData | null) => void
}

export const useTabStore = create<TabState>()(
  devtools(
    (set) => ({
      tabs: [],
      setTabs: (tabs: BrowserTabListItem[]) => set({ tabs }),
      activeTab: null,
      setActiveTab: (activeTab: BrowserActiveTabData | null) => set({ activeTab }),
    }),
    {
      name: 'TabStore',
    },
  ),
)
