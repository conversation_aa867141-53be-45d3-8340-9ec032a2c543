import type { Account } from '@renderer/infrastructure/model'
import { type ImageFileInfo, type VideoFileInfo } from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'

import type { AccountSession, WithLocalStorage } from '@common/structure'
import type {
  LocalArticlePushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { AiqiyiArticleTaskInput, AiqiyiVideoTaskInput } from '@yixiaoer/platform-service'
import { platformNames } from '@common/model/platform-name'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'

class QiEHaoPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    const platformConfig = pushingConfig.platformConfigs[platformNames.AiQiYi]
    const categories = platformConfig.categories.map((x) => x.raw)

    const tags = pushingConfig.tags

    if (!tags) {
      throw new Error('爱奇艺标签不能为空')
    }

    return {
      localStorage: session.localStorage,
      taskId: taskId,
      video: {
        duration: pushingConfig.video!.fileDurationTimeSpan.seconds,
        width: pushingConfig.video!.videoWidth,
        height: pushingConfig.video!.videoHeight,
        size: pushingConfig.video!.fileSize,
        localPath: pushingConfig.video!.filePath,
      },
      cover: {
        width: pushingConfig.cover!.width,
        height: pushingConfig.cover!.height,
        size: pushingConfig.cover!.size,
        pathOrUrl: pushingConfig.cover!.path,
      },
      desc: description,
      title: title,
      /**
       * 标签
       */
      tags: tags,
      /**
       * 定时发布13位时间戳
       */
      publishTime: pushingConfig.timing,
      /**
       * 创作类型 1:原创 0:转载
       */
      isOriginal: pushingConfig.isOriginal ? 1 : 0,
      pubType: toDraft ? 0 : 1,
      category: categories && categories[0] ? categories[0] : undefined,
    } satisfies AiqiyiVideoTaskInput & WithLocalStorage
  }

  async toArticle(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalArticlePushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      title: config.title,
      content: config.content,
      pubType: toDraft ? 0 : 1,
      cancelToken: undefined,
    } satisfies AiqiyiArticleTaskInput & WithLocalStorage
  }
}

export const aiQiYi = new QiEHaoPushingTaskBodyConverter()
