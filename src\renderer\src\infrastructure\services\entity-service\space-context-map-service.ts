import type { SpaceContextMapStoreObject } from '@renderer/infrastructure/entity'
import type { SpaceContextMapRepository } from '@renderer/infrastructure/repository/repositories/space-context-map'
import { useSpaceContextMapRepository } from '@renderer/infrastructure/repository/repositories/space-context-map'
import { identifierService } from '@renderer/infrastructure/services'
import { useMemo } from 'react'

export class SpaceContextMapService {
  constructor(private spaceContextMapRepository: SpaceContextMapRepository) {}

  async getContextInfo(spaceId: string) {
    const map = await this.spaceContextMapRepository.tryGet(spaceId)
    return map ? { contextId: map.contextId, originalChecksum: map.originalChecksum } : null
  }

  async createNewContextInfo(spaceId: string, checksum: string | null = null) {
    const contextId = identifierService.generateUUID()
    await this.spaceContextMapRepository.save({
      spaceId,
      contextId,
      originalChecksum: checksum,
    } satisfies SpaceContextMapStoreObject as SpaceContextMapStoreObject)
    return { contextId, originalChecksum: null }
  }

  async getSpaceId(contextId: string) {
    const map = await this.spaceContextMapRepository.tryGetOne((x) =>
      x.where('contextId').equals(contextId).toArray(),
    )
    return map?.spaceId ?? null
  }

  async getAllContextIds() {
    return (await this.spaceContextMapRepository.getMany()).map((x) => x.contextId)
  }

  async removeMap(spaceId: string) {
    await this.spaceContextMapRepository.delete(spaceId)
  }

  async setOriginalChecksum(id: string, checksum: string) {
    let spaceContextMapStoreObject = await this.spaceContextMapRepository.tryGet(id)
    if (spaceContextMapStoreObject) {
      spaceContextMapStoreObject.originalChecksum = checksum
    } else {
      spaceContextMapStoreObject = {
        spaceId: id,
        contextId: identifierService.generateUUID(),
        originalChecksum: checksum,
      } satisfies SpaceContextMapStoreObject as SpaceContextMapStoreObject
    }
    await this.spaceContextMapRepository.save(spaceContextMapStoreObject)
  }

  async isContextMapped(contextId: string): Promise<boolean> {
    const count = await this.spaceContextMapRepository.count((x) =>
      x.where('contextId').equals(contextId),
    )
    return count > 0
  }

  async getChecksum(contextId: string) {
    const map = await this.spaceContextMapRepository.tryGetOne((x) =>
      x.where('contextId').equals(contextId).toArray(),
    )
    return map?.originalChecksum ?? null
  }

  async hasContext(spaceId: string) {
    const count = await this.spaceContextMapRepository.count((x) =>
      x.where('spaceId').equals(spaceId),
    )
    return count > 0
  }
}

export function useSpaceContextMapService() {
  const spaceContextMapRepository = useSpaceContextMapRepository()
  return useMemo(
    () => new SpaceContextMapService(spaceContextMapRepository),
    [spaceContextMapRepository],
  )
}
