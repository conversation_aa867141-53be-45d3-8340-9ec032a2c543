import { Repository } from '../index'
import type { PushingTaskReportingStateStoreObject } from '../../entity'
import type { MemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemo } from 'react'

export function usePushingTaskReportingStateRepository() {
  const database = useMemberDatabase()
  return useMemo(() => new PushingTaskReportingStateRepository(database), [database])
}

export class PushingTaskReportingStateRepository extends Repository<
  PushingTaskReportingStateStoreObject,
  'taskSetId'
> {
  constructor(database: MemberDatabase) {
    super(database.instance.PushingTaskSetReportingState)
  }
}
