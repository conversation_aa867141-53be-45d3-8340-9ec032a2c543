import { useTabStore } from '@browser/store/tabStore'
import GoBackIcon from '@browser/assets/goBack.svg?react'
import GoForwardIcon from '@browser/assets/goForward.svg?react'
import RefreshIcon from '@browser/assets/refresh.svg?react'
import FavoriteIcon from '@browser/assets/favorite.svg?react'
import FavoritedIcon from '@browser/assets/favorited.svg?react'
import { useBrowserService } from '@browser/infrastructure/services/browser-service'
import { AddToFavorites } from '@browser/pages/components/add-to-favorites'
import { useFavoriteStore } from '@browser/store/favoriteStore'

export function Actions() {
  const { activeTab } = useTabStore()
  const browserService = useBrowserService()
  const { isInFavorite: storeIsInFavorite, getFavorite: storeGetFavorite } = useFavoriteStore()

  const isInFavorite = () => {
    if (!activeTab?.contextIdentifier.contextId) return false
    return storeIsInFavorite(activeTab.contextIdentifier.contextId, activeTab.url)
  }

  return (
    <div className="mx-4 flex flex-shrink-0 flex-row items-center justify-center gap-1">
      <button
        className={`btn btn-primary cursor-pointer rounded p-1 disabled:cursor-default ${activeTab?.canGoBack ? 'hover:bg-accent' : ''}`}
        onClick={browserService.goBack}
        disabled={!activeTab?.canGoBack}
      >
        <GoBackIcon className={activeTab?.canGoBack ? 'text-[#1C252E]' : 'text-[#BDBDBD]'} />
      </button>
      <button
        className={`btn btn-primary cursor-pointer rounded p-1 disabled:cursor-default ${activeTab?.canGoForward ? 'hover:bg-accent' : ''}`}
        onClick={browserService.goForward}
        disabled={!activeTab?.canGoForward}
      >
        <GoForwardIcon className={activeTab?.canGoForward ? 'text-[#1C252E]' : 'text-[#BDBDBD]'} />
      </button>
      <button
        className="btn btn-primary cursor-pointer rounded p-1 hover:bg-accent disabled:cursor-default"
        onClick={browserService.refresh}
      >
        <RefreshIcon />
      </button>
      {activeTab && activeTab.canAddFavorite && !window.__ident__ && (
        <AddToFavorites
          tab={activeTab}
          favorite={storeGetFavorite(activeTab.contextIdentifier.contextId, activeTab.url)}
        >
          <div className="btn btn-primary cursor-pointer rounded p-1 hover:bg-accent disabled:cursor-default">
            {isInFavorite() ? (
              <FavoritedIcon className="text-[#1C252E]"></FavoritedIcon>
            ) : (
              <FavoriteIcon className="text-primary"></FavoriteIcon>
            )}
          </div>
        </AddToFavorites>
      )}
    </div>
  )
}
