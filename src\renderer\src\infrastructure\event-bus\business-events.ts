import type {
  SpiderAccount,
  PushingTaskSetIdentifier,
  PushingTaskSetState,
  Account,
} from '../model'
import type {
  AppTaskPushingEventParam,
  ScriptUpdatedEventParam,
  VipUpgradeEventParam,
} from '@renderer/hooks/preload/team/useSocketPreload'
import { Event } from '@renderer/infrastructure/event-bus/event'

export const systemEvents = {
  exitConfirming: new Event(Symbol()),
  deviceLogDownload: new Event<string>(Symbol()),
}

/**
 * @deprecated 这是较早期事件不多时用的统称，后续应该按业务划分，现存的事件逐步重构
 */
export const businessEvents = {
  scriptUpdated: new Event<ScriptUpdatedEventParam>(Symbol()),
}

export const advertiseEvents = {
  // 广告已更新
  advertiseUpdated: new Event(Symbol()),
}

export const notificationEvents = {
  // 有新消息
  newNotification: new Event(Symbol()),
  systemNotifyUpdated: new Event(Symbol()),
}

export const loginEvents = {
  // 登录成功
  loginOnAnotherDevice: new Event(Symbol()),
}

export const websocketEvents = {
  // 账号已更新
  accountUpdated: new Event<string>(Symbol()),
}

export const authorizeEvents = {
  // 账号已新增
  accountAdded: new Event<string>(Symbol()),
  // 账号已更新
  accountUpdated: new Event<Account>(Symbol()),
  // 账号已删除
  accountDeleted: new Event<string>(Symbol()),
  // 账号从浏览器授权成功
  accountAuthorizeSuccess: new Event<SpiderAccount>(Symbol()),
}

export const pushEvents = {
  // 推送任务准备完成
  pushingTaskPrepared: new Event(Symbol()),
  // 本地推送结果改变
  pushResultChanged: new Event<string>(Symbol()),
  // 推送任务已完成
  pushTaskCompleted: new Event<{
    taskId: string
    wechatLockToken: [string, string] | null
  }>(Symbol()),
  // 云端推送结果改变
  cloudPushResultChanged: new Event<string>(Symbol()),
  // 推送任务进度更新
  pushingProgressChanged: new Event<{ taskId: string; progress: number }>(Symbol()),
  // 本地审核状态更新
  auditResultUpdated: new Event<string>(Symbol()),
  // 推送任务已删除
  pushingSetStateChanged: new Event<{
    taskSetId: PushingTaskSetIdentifier
    state: PushingTaskSetState
    hasFailedTask: boolean
  }>(Symbol()),

  // app应用任务推送
  appTaskPushing: new Event<AppTaskPushingEventParam[]>(Symbol()),

  // 推送任务删除
  pushingTaskDeleted: new Event<PushingTaskSetIdentifier[] | PushingTaskSetIdentifier>(Symbol()),
}

export const teamEvents = {
  // 团队信息已改变
  teamDetailChanged: new Event<string>(Symbol()),
  // 团队版本升级
  teamVersionUpgrade: new Event<VipUpgradeEventParam>(Symbol()),
  // 团队版本降级
  teamVersionDowngrade: new Event(Symbol()),
  // 已被踢出团队
  beenRemoved: new Event<string>(Symbol()),
  // 角色已变更
  roleChanged: new Event<string>(Symbol()),
  // 团队已加入
  teamJoined: new Event(Symbol()),

  // 成员已邀请
  memberInvited: new Event<string>(Symbol()),
  // 成员被移除
  memberRemoved: new Event<string>(Symbol()),
}

export const assertLibraryEvents = {
  capacityUpdated: new Event(Symbol()),
}

export const authenticationEvents = {
  serverError: new Event<string>(Symbol()),
  // 未授权
  unauthorized: new Event(Symbol()),
  businessError: new Event<string>(Symbol()),
  // 需vip权限
  needVip: new Event(Symbol()),
}

export const spaceEvents = {
  webSpaceDumpUpdated: new Event<string>(Symbol()),
  accountSpaceDumpUpdated: new Event<{ spaceId: string; accountId: string }>(Symbol()),
  FavoritesUpdated: new Event(Symbol()),
  // 空间已创建
  spaceCreated: new Event<string>(Symbol()),
  // 空间已更新
  spaceUpdated: new Event<string>(Symbol()),
  // 空间图标已更新
  spaceIconUpdated: new Event<{
    spaceId: string
    icon: string | null
  }>(Symbol()),
}
