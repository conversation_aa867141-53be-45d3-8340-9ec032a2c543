import { useMemo } from 'react'
import { Repository } from '../index'

import type { MemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemberDatabase } from '@renderer/infrastructure/repository/storage'
import type { OverviewStoreObject } from '@renderer/infrastructure/entity/overview-entity'

export class OverviewRepository extends Repository<OverviewStoreObject, 'accountId'> {
  constructor(database: MemberDatabase) {
    super(database.instance.Overview)
  }
}

export function useOverviewRepository() {
  const database = useMemberDatabase()

  return useMemo(() => new OverviewRepository(database), [database])
}
