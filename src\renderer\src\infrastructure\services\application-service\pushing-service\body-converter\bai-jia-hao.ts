import {
  platforms,
  type Account,
  type ImageFileInfo,
  type VideoFileInfo,
} from '@renderer/infrastructure/model'
import { PushContentType } from '@common/model/content-type'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import { constants } from '@coozf/editor'
import { ignoreException } from './pushing-task-body-converter'
import { baiJiaHaoPlatformService, datetimeService } from '@renderer/infrastructure/services'
import type {
  BaijiahaoLocationItem,
  BaijiahaoTopicItem,
  BaijiahaoVideoTaskInput,
} from '@yixiaoer/platform-service'
import type { AccountSession, PlatformDataItem, WithLocalStorage } from '@common/structure'
import type { BaijiahaoMiniVideoTaskInput } from '@yixiaoer/platform-service/dist/media-platform/baijiahao/baijiahao-mini-video'
import type {
  LocalArticlePushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { BaijiahaoArticleTaskInput } from '@yixiaoer/platform-service/dist/media-platform/baijiahao/baijiahao-article'

async function toVerticalVideo(
  taskId: string,
  account: Account,
  session: AccountSession,
  description: string,
  location: PlatformDataItem<BaijiahaoLocationItem> | undefined,
  video: VideoFileInfo,
  cover: ImageFileInfo,
  title: string,
  toDraft: boolean,
  timing?: number,
) {
  // 转换话题
  let topic: PlatformDataItem<BaijiahaoTopicItem> | undefined
  await ignoreException(async () => {
    // 创建一个DOMParser实例
    const parser = new DOMParser()
    // 将HTML字符串解析为一个Document对象
    const doc = parser.parseFromString(description, 'text/html')
    // 找到你想要修改的元素
    const elements = doc.querySelectorAll(`${constants.commonTopic.topicTagName}[text]`)

    for (const element of elements) {
      const keyword = element.getAttribute('text')
      if (!keyword) continue
      // 获取话题
      const topics = await baiJiaHaoPlatformService.getTopics(session, keyword, keyword)
      topic = topics[0] ?? undefined
      if (topic) break
    }
  })

  return {
    localStorage: session.localStorage,
    taskId: taskId,
    video: {
      duration: video.fileDurationTimeSpan.seconds,
      width: video.videoWidth,
      height: video.videoHeight,
      size: video.fileSize,
      localPath: video.filePath,
    },
    cover: {
      width: cover.width,
      height: cover.height,
      size: cover.size,
      pathOrUrl: cover.path,
    },
    verticalCover: {
      width: cover.width,
      height: cover.height,
      size: cover.size,
      pathOrUrl: cover.path,
    },
    pubType: toDraft ? 0 : 1,
    title: title,
    topic: topic?.raw,
    tags: [],
    location: location?.raw,
    original: undefined,
    isAigc: undefined,
    reward: undefined,
    activityId: undefined,
    categoryName: undefined,
    subCategoryName: undefined,
    publishTime: datetimeService.toSeconds(timing),
    //@ts-ignore 等待类型修复为可空
    cancelToken: undefined,
  } satisfies BaijiahaoMiniVideoTaskInput & WithLocalStorage
}

async function toHorizonVideo(
  taskId: string,
  account: Account,
  session: AccountSession,
  description: string,
  location: PlatformDataItem<BaijiahaoLocationItem> | undefined,
  video: VideoFileInfo,
  cover: ImageFileInfo,
  title: string,
  toDraft: boolean,
  timing?: number,
) {
  // 转换话题
  let topic: PlatformDataItem<BaijiahaoTopicItem> | undefined
  await ignoreException(async () => {
    // 创建一个DOMParser实例
    const parser = new DOMParser()
    // 将HTML字符串解析为一个Document对象
    const doc = parser.parseFromString(description, 'text/html')
    // 找到你想要修改的元素
    const elements = doc.querySelectorAll(`${constants.commonTopic.topicTagName}[text]`)

    for (const element of elements) {
      const keyword = element.getAttribute('text')
      if (!keyword) continue
      // 获取话题
      const topics = await baiJiaHaoPlatformService.getTopics(session, keyword, keyword)
      topic = topics[0] ?? undefined
      if (topic) break
    }
  })

  return {
    localStorage: session.localStorage,
    taskId: taskId,
    video: {
      duration: video.fileDurationTimeSpan.seconds,
      width: video.videoWidth,
      height: video.videoHeight,
      size: video.fileSize,
      localPath: video.filePath,
    },
    cover: {
      width: cover.width,
      height: cover.height,
      size: cover.size,
      pathOrUrl: cover.path,
    },
    verticalCover: {
      width: cover.width,
      height: cover.height,
      size: cover.size,
      pathOrUrl: cover.path,
    },
    pubType: toDraft ? 0 : 1,
    title: title,
    desc: description,
    topic: topic?.raw,
    tags: [],
    location: location?.raw,
    //@ts-ignore 等待类型修复为可空
    cancelToken: undefined,
    publishTime: datetimeService.toSeconds(timing),
  } satisfies BaijiahaoVideoTaskInput & WithLocalStorage
}

class BaiJiaHaoPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    // 获取地理位置
    let location: PlatformDataItem<BaijiahaoLocationItem> | undefined
    await ignoreException(async () => {
      const configLocation = pushingConfig.location
      if (configLocation && configLocation[platforms.BaiJiaHao.name]) {
        location = configLocation[
          platforms.BaiJiaHao.name
        ] as PlatformDataItem<BaijiahaoLocationItem>
      } else {
        location = locationKeyword
          ? (await baiJiaHaoPlatformService.getLocations(account, session, locationKeyword))[0] ??
            null
          : undefined
      }
    })

    if (contentType === PushContentType.VerticalVideo) {
      return await toVerticalVideo(
        taskId,
        account,
        session,
        description,
        location,
        video,
        cover,
        title,
        toDraft,
        timing,
      )
    } else {
      return await toHorizonVideo(
        taskId,
        account,
        session,
        description,
        location,
        video,
        cover,
        title,
        toDraft,
        timing,
      )
    }
  }

  async toArticle(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalArticlePushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      pubType: toDraft ? 0 : 1,
      title: config.title,
      covers: [
        {
          width: config.cover!.width,
          height: config.cover!.height,
          size: config.cover!.size,
          pathOrUrl: config.cover!.path,
        },
      ],
      verticalCover: config.verticalCover!.path,
      verticalCoverWidth: config.verticalCover!.width,
      verticalCoverHeight: config.verticalCover!.height,
      content: config.content,
      categoryName: config.category,
      //@ts-ignore 等待类型修复为可空
      cancelToken: undefined,
      publishTime: config.timing ?? undefined,
    } satisfies BaijiahaoArticleTaskInput & WithLocalStorage
  }
}

export const baiJiaHao = new BaiJiaHaoPushingTaskBodyConverter()
