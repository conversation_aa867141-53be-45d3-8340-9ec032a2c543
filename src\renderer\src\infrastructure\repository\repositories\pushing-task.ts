import { Repository } from '../index'
import type { PushingTaskStoreObject } from '../../entity'
import type { MemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemo } from 'react'

export function usePushingTaskRepository() {
  const database = useMemberDatabase()
  return useMemo(() => new PushingTaskRepository(database), [database])
}

export class PushingTaskRepository extends Repository<PushingTaskStoreObject, 'taskId'> {
  constructor(database: MemberDatabase) {
    super(database.instance.PushingTask)
  }
}
