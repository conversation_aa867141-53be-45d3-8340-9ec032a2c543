import type { PushingTaskReportingStateRepository } from '../../repository/repositories'
import { usePushingTaskReportingStateRepository } from '../../repository/repositories'
import type { PushingTaskReportingStateStoreObject } from '../../entity'
import { useMemo } from 'react'
import type { PushingTaskSetIdentifier } from '@renderer/infrastructure/model'

export function usePushingTaskSetReportingStateStore() {
  const repository = usePushingTaskReportingStateRepository()
  return useMemo(() => new PushingTaskSetReportingStateStore(repository), [repository])
}

export class PushingTaskSetReportingStateStore {
  constructor(private repository: PushingTaskReportingStateRepository) {}

  async markAsUnreported(taskSetId: PushingTaskSetIdentifier) {
    await this.repository.save({
      taskSetId: taskSetId,
      reported: 0,
    })
  }

  async markAsReported(PushingTaskSetIdentifier: PushingTaskSetIdentifier) {
    await this.repository.save({
      taskSetId: PushingTaskSetIdentifier,
      reported: 1,
    })
  }

  async markAllAsReported(taskSetIds: PushingTaskSetIdentifier[]) {
    await this.repository.bulkSave(
      taskSetIds.map(
        (taskId) =>
          ({
            taskSetId: taskId,
            reported: 1,
          }) satisfies PushingTaskReportingStateStoreObject,
      ),
    )
  }

  async isReported(taskSetId: PushingTaskSetIdentifier) {
    return !!(await this.repository.tryGet(taskSetId))?.reported
  }

  async getUnreportedTaskSetIds(): Promise<PushingTaskSetIdentifier[]> {
    return (
      await this.repository.getMany((table) => table.where('reported').equals(0).toArray())
    ).map((entity) => entity.taskSetId)
  }

  // del
  async delete(taskSetId: PushingTaskSetIdentifier | PushingTaskSetIdentifier[]) {
    if (Array.isArray(taskSetId)) {
      return await this.repository.bulkDelete(taskSetId)
    } else {
      return await this.repository.delete(taskSetId)
    }
  }
}
