import type { PushingTaskSetStoreObject } from '@renderer/infrastructure/entity/pushing-task-set-entity'
import type { PushingTaskSetIdentifier } from '@renderer/infrastructure/model'
import { getEditContentTypeByName } from '@renderer/infrastructure/model'
import { PushingTaskSet } from '@renderer/infrastructure/model'
import type { PushingTaskSetRepository } from '@renderer/infrastructure/repository/repositories'
import { usePushingTaskSetRepository } from '@renderer/infrastructure/repository/repositories'
import { useMemo } from 'react'

function _Model2Object(pushingTask: PushingTaskSet) {
  return {
    taskSetId: pushingTask.taskSetId,
    contentTypeName: pushingTask.contentType,
    platformNames: pushingTask.platformNames,
    thumbUrl: pushingTask.thumbUrl,
    brief: pushingTask.brief,
    createTime: pushingTask.createTime,
    state: pushingTask.state,
    fromApp: pushingTask.fromApp,
    hasFailedTask: pushingTask.hasFailedTask,
  } satisfies PushingTaskSetStoreObject as PushingTaskSetStoreObject
}

function _Object2Model(entity: PushingTaskSetStoreObject) {
  return PushingTaskSet.fromStore(
    entity.taskSetId as PushingTaskSetIdentifier,
    getEditContentTypeByName(entity.contentTypeName),
    entity.platformNames,
    entity.thumbUrl,
    entity.brief,
    entity.createTime,
    entity.state,
    entity.fromApp,
    entity.hasFailedTask,
  )
}

export function usePushingTaskSetStore() {
  const pushingTaskRepository = usePushingTaskSetRepository()
  return useMemo(() => new PushingTaskSetStore(pushingTaskRepository), [pushingTaskRepository])
}

export class PushingTaskSetStore {
  constructor(private repository: PushingTaskSetRepository) {}

  async save(pushingTaskSet: PushingTaskSet) {
    return this.repository.save(_Model2Object(pushingTaskSet))
  }

  async getList() {
    const storeObjects = await this.repository.getMany()
    return storeObjects.map(_Object2Model)
  }

  async getMany(taskSetIds: PushingTaskSetIdentifier[]) {
    const storeObjects = await this.repository.getMany((x) =>
      x.where('taskSetId').anyOf(taskSetIds).toArray(),
    )
    return storeObjects.map(_Object2Model)
  }

  async get(taskSetId: PushingTaskSetIdentifier) {
    const storeObject = await this.repository.get(taskSetId)
    return _Object2Model(storeObject)
  }

  async getEntireSets() {
    const storeObjects = await this.repository.getMany()
    return storeObjects.map(_Object2Model)
  }

  // del
  async delete(taskSetId: string | string[]) {
    if (Array.isArray(taskSetId)) {
      return await this.repository.bulkDelete(taskSetId)
    } else {
      return await this.repository.delete(taskSetId)
    }
  }
}
