import { useBrowserService } from '@browser/infrastructure/services/browser-service'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@renderer/shadcn-components/ui/tooltip'
import { Send } from 'lucide-react'
import WarningIcon from '../assets/warning.svg?react'
import UploadIcon from '../assets/upload.svg?react'
import SucessIcon from '../assets/sucess.svg?react'
import { useEffect, useRef, useState } from 'react'

export function RpaActions() {
  const browserService = useBrowserService()
  const [pushCount, setPushCount] = useState(0)
  const [totalCount, setTotalCount] = useState(0)
  const cancel = useRef(false)
  const [isRap, setIsRpa] = useState(false)
  const [isDone, setIsDone] = useState(false)
  const [isUnderway, setIsUnderway] = useState(false)

  useEffect(() => {
    try {
      if (window.__ident__) {
        setIsRpa(!!window.__ident__)
        setTotalCount(JSON.parse(window.__ident__)?.num || 0)
      }
    } catch (e) {
      console.error(e)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [window.__ident__])

  if (!isRap) {
    return null
  }

  return (
    <TooltipProvider>
      <Tooltip
        delayDuration={200}
        onOpenChange={(open) => {
          if (open) {
            browserService.setHeaderViewTop()
          } else {
            browserService.setTabViewTop()
          }
        }}
      >
        <TooltipTrigger asChild>
          <Button variant="ghost" size="sm" className="mr-2 hover:bg-[#919eab30]">
            {isDone ? <SucessIcon className="h-[14px] w-[14px] text-[#000]" /> : <Send size={14} />}
            {isUnderway && '发布中...'}
            {pushCount === 0 && !isDone && '批量发布'}
            {isDone && '已发布'}
          </Button>
        </TooltipTrigger>
        <TooltipContent
          align="center"
          className="flex w-[280px] items-center border border-[#D1D5DB] bg-white p-4 text-stone-950 shadow-xl"
        >
          {isUnderway && (
            <div className="flex w-full items-center justify-between gap-4">
              <span className="flex items-center gap-2">
                <UploadIcon />
                发布中 {pushCount}/{totalCount} 个
              </span>
            </div>
          )}
          {pushCount === 0 && !isDone && (
            <div className="flex w-full flex-col gap-4">
              <span className="flex items-center gap-2 text-[16px]">
                <WarningIcon />
                请确保所有表单都已填写完整
              </span>
              <Button
                className="w-[80px] self-end"
                size="sm"
                onClick={async () => {
                  const data = JSON.parse(window.__ident__) as { num: number }
                  if (data && data.num && typeof data.num === 'number') {
                    setIsUnderway(true)
                    for (let i = 0; i < data.num; i++) {
                      if (cancel.current) {
                        break
                      }

                      try {
                        const res = window.api.sendSync('publish-bulk-event', window.__ident__, i)
                        console.log(res)
                        if (res) {
                          setPushCount((prev) => prev + 1)
                        }
                      } catch {
                        //
                      }
                    }
                    setIsUnderway(false)
                    setIsDone(true)
                  }
                }}
              >
                确认发布
              </Button>
            </div>
          )}
          {isDone && (
            <div className="flex w-full flex-col gap-4">
              <div className="flex items-center gap-2">
                <SucessIcon className="text-[#47a65f]" />
                <span className="flex items-center text-[16px]">执行成功: {pushCount}</span>
                <span className="ml-2 flex items-center text-[16px]">
                  执行失败: <b className="pl-1 text-[#ef5757]">{totalCount - pushCount}</b>
                </span>
              </div>
              <Button
                className="w-[80px] self-end"
                size="sm"
                onClick={() => {
                  setIsDone(false)
                  setIsUnderway(false)
                  setPushCount(0)
                }}
              >
                重新发布
              </Button>
            </div>
          )}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
