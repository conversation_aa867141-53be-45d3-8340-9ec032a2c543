import type { Account } from '@renderer/infrastructure/model'
import { type ImageFileInfo, type VideoFileInfo } from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import { ignoreException } from './pushing-task-body-converter'
import type { AccountSession, WithLocalStorage } from '@common/structure'
import type { LocalVideoPushingConfig } from '@renderer/infrastructure/model/config/pushing-config'
import type { WeishiVideoTaskInput } from '@yixiaoer/platform-service'
import { utils } from '@coozf/editor'
import { tengXunWeiShiPlatformService } from '@renderer/infrastructure/services'

class TengXunWeiShiPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    description = await this.getDescription(description, session)

    return {
      localStorage: session.localStorage,
      taskId: taskId,
      video: {
        duration: pushingConfig.video!.fileDurationTimeSpan.seconds,
        width: pushingConfig.video!.videoWidth,
        height: pushingConfig.video!.videoHeight,
        size: pushingConfig.video!.fileSize,
        localPath: pushingConfig.video!.filePath,
      },
      cover: {
        width: pushingConfig.cover!.width,
        height: pushingConfig.cover!.height,
        size: pushingConfig.cover!.size,
        pathOrUrl: pushingConfig.cover!.path,
      },
      desc: description,
      publishTime: pushingConfig.timing,
      visibleType: toDraft ? 1 : 0,
    } satisfies WeishiVideoTaskInput & WithLocalStorage
  }

  private async getDescription(description: string, session: AccountSession): Promise<string> {
    return await utils.commonTopic.modifyTopics(description, async (element) => {
      await ignoreException(async () => {
        const keyword = element.getAttribute('text')
        if (!keyword) return

        const topic =
          (await tengXunWeiShiPlatformService.getTopics(session, keyword))[0] ?? undefined
        if (!topic) element.remove()
        else element.setAttribute('raw', JSON.stringify(topic.raw))
      })
    })
  }
}

export const tengXunWeiShi = new TengXunWeiShiPushingTaskBodyConverter()
