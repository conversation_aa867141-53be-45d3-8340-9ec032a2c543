import { useUserApiService } from '@renderer/infrastructure/services/network-service'
import { useMemo } from 'react'
import type { ApiService } from '@renderer/infrastructure/services/network-service/api-service'

export function useUserApi() {
  const userApiService = useUserApiService()
  return useMemo(() => new UserApi(userApiService), [userApiService])
}

export class UserApi {
  constructor(private userApiService: ApiService) {}

  async switchAuthenticTeam(teamId: string) {
    return this.userApiService.post<{
      authorization: string
    }>(`/teams/${teamId}/auth`, {})
  }

  getUserInfo() {
    return this.userApiService.get<{
      id: string
      avatarKey: string
      avatarUrl: string
      createdAt: number
      nickName: string
      phone: string
      account: string
      updatedAt: string
      latestTeamId: string
      isPassword: boolean
    }>('/users/info')
  }

  logout() {
    return this.userApiService.delete<void>('/users/auth')
  }

  updateUserInfo(nickName: string | null, avatarKey: string | null) {
    const data = {} as Record<string, string>
    if (nickName) {
      data.nickName = nickName
    }
    if (avatarKey) {
      data.avatarKey = avatarKey
    }
    return this.userApiService.patch<{
      id: string
      phone: string
      name: string
      avatarUrl: string
      avatarKey: string
      latestTeamId: string
    }>('/users/info', data)
  }
}
