import { describe, expect, it } from 'vitest'
import { getPushingTaskSetState } from './pushing-task-set'

describe('PushingTaskSetState', () => {
  it('应该正确的计算发布集状态', () => {
    expect(getPushingTaskSetState(['发布中', '发布中', '成功'])).toEqual('部分发布成功')
    expect(getPushingTaskSetState(['发布中', '发布中', '发布中'])).toEqual('发布中')
    expect(getPushingTaskSetState(['发布中', '发布中', '失败'])).toEqual('发布中')
    expect(getPushingTaskSetState(['失败', '失败', '失败'])).toEqual('全部发布失败')
    expect(getPushingTaskSetState(['成功', '成功', '成功'])).toEqual('全部发布成功')
  })
})
