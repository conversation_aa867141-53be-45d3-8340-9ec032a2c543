import type { MemberResponse } from '@renderer/infrastructure/types/MemberResponse'
import type { InviteUserResponse } from '@renderer/infrastructure/types/InviteUserResponse'
import { Member, InviteUser } from '@renderer/infrastructure/model'
import type { PagedRequestOption } from '@renderer/infrastructure/types/paged-request-option'
import { useUserApiService } from '@renderer/infrastructure/services/network-service'
import { useMemo } from 'react'
import type { ApiService } from '@renderer/infrastructure/services/network-service/api-service'
import type { PagedResponse } from '@renderer/infrastructure/types'

export function useMemberApi() {
  const userApiService = useUserApiService()
  return useMemo(() => new MemberApi(userApiService), [userApiService])
}

export class MemberApi {
  constructor(private userApiService: ApiService) {}

  async getActiveAllMembers() {
    const members = await this.getMemberList({ size: 9999, page: 1 }, ['active'])

    return members.data
  }

  private _2Member(data: MemberResponse) {
    return new Member(
      data.id,
      data.phone,
      data.nickName,
      data.remark,
      data.avatarUrl,
      data.status,
      data.roles,
      data.createdAt,
      data.accountCount,
      data.browserCount,
      data.isFreeze,
      data.memberId,
      data.maxAccountCount,
    )
  }

  async getMemberList(pageQuery: PagedRequestOption, statuses?: string[]) {
    const params = {
      size: pageQuery.size.toString(),
      page: pageQuery.page.toString(),
    } as Record<string, string | string[]>
    if (Array.isArray(statuses) && statuses.length > 0) {
      params.statuses = statuses
    }

    const result = await this.userApiService.get<PagedResponse<MemberResponse>>(`/members`, params)
    return {
      ...result,
      data: result.data.map(this._2Member),
    } satisfies PagedResponse<Member> as PagedResponse<Member>
  }

  async getUserStatus(phone: string) {
    const result = await this.userApiService.get<InviteUserResponse>(`/invitations/user-status`, {
      phone: phone,
    })

    return new InviteUser(result.id, result.nickName, result.avatarUrl, result.status)
  }

  createInvitation(phone: string) {
    return this.userApiService.post(`/invitations`, {
      phone: phone,
    })
  }

  async setMaxAccountCount(userId: string, count: number) {
    return this.userApiService.put(`/members/${userId}/account-count`, count)
  }

  async getCurrentUserMaxAccountCount() {
    const result = await this.userApiService.get<{
      maxAccountCount: number
      currentAccountCount: number
    }>(`/members/max-account-count`)
    return result
  }

  async unFreezeMember(memberId: string) {
    return this.userApiService.put(`/members/${memberId}/freeze`, {
      isFreeze: false,
    })
  }

  async getMemberInfo(userId: string) {
    const result = await this.userApiService.get<MemberResponse>(`/members/${userId}`)
    return this._2Member(result)
  }

  setMemberRoles(userId: string, roles: string[]) {
    return this.userApiService.put(`/members/${userId}/roles`, roles)
  }

  setMemberAccounts(userId: string, accounts: string[]) {
    return this.userApiService.put(`/members/${userId}/accounts`, accounts)
  }

  getMemberAccounts(memberId: string) {
    return this.userApiService.get<string[]>(`/members/${memberId}/accounts`)
  }

  getMemberWebSpaces(memberId: string) {
    return this.userApiService.get<string[]>(`/members/${memberId}/browsers`)
  }

  quitTeam(userId: string) {
    return this.userApiService.delete(`/members/${userId}`)
  }

  removeMember(userId: string) {
    return this.userApiService.delete<void>(`/members/${userId}`)
  }
  // 同意加入团队

  approveJoinTeam(invitationId: string) {
    return this.userApiService.patch(`/invitations/${invitationId}`, { approved: true })
  }
  // 拒绝加入团队

  rejectJoinTeam(invitationId: string) {
    return this.userApiService.patch(`/invitations/${invitationId}`, { approved: false })
  }
  // 同意团队申请

  approveTeamJoinApply(proposalId: string) {
    return this.userApiService.patch(`/proposals/${proposalId}`, { approved: true })
  }
  // 拒绝团队申请

  rejectTeamJoinApply(proposalId: string) {
    return this.userApiService.patch(`/proposals/${proposalId}`, { approved: false })
  }

  setMemberRemark(memberId: string, remark: string) {
    return this.userApiService.put(`/members/${memberId}/remark`, { remark })
  }
}
