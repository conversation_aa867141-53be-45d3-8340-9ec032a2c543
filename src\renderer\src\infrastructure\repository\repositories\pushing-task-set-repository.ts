import { type MemberDatabase, useMemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemo } from 'react'
import { Repository } from '@renderer/infrastructure/repository'
import type { PushingTaskSetStoreObject } from '@renderer/infrastructure/entity/pushing-task-set-entity'

export function usePushingTaskSetRepository() {
  const database = useMemberDatabase()
  return useMemo(() => new PushingTaskSetRepository(database), [database])
}

export class PushingTaskSetRepository extends Repository<PushingTaskSetStoreObject, 'taskSetId'> {
  constructor(database: MemberDatabase) {
    super(database.instance.PushingTaskSet)
  }
}
