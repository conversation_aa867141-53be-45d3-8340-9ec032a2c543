import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { ContextFavorite } from '@common/structure/space/context-favorite'

interface FavoriteState {
  favorites: Record<string, ContextFavorite[]>
  setFavorites: (favorites: Record<string, ContextFavorite[]>) => void
  isInFavorite: (contextId: string, url: string) => boolean
  getFavorite: (contextId: string, url: string) => ContextFavorite | null
  canAddFavorite: (contextId: string) => boolean
}

export const useFavoriteStore = create<FavoriteState>()(
  devtools(
    (set, get) => ({
      favorites: {},
      setFavorites: (favorites: Record<string, ContextFavorite[]>) => set({ favorites }),
      isInFavorite: (contextId: string, url: string) => {
        return get().favorites[contextId]?.some((favorite) => favorite.url === url) ?? false
      },
      getFavorite: (contextId: string, url: string) => {
        return get().favorites[contextId]?.find((favorite) => favorite.url === url) ?? null
      },
      canAddFavorite: (contextId: string) => {
        return !get().favorites[contextId] || get().favorites[contextId].length < 10
      },
    }),
    {
      name: 'FavoriteStore',
    },
  ),
)
