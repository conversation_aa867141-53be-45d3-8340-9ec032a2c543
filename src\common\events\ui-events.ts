export const uiEvents = {
  showOpenDialogSync: 'showOpenDialogSync',
  showSaveDialogSync: 'showSaveDialogSync',
  saveCSVFile: 'saveCSVFile',
  getVideoInfo: 'getVideoInfo',
  getImageInfo: 'getImageInfo',
  saveImageFile: 'saveImageFile',
  saveVideoFile: 'saveVideoFile',
  copyToClipboard: 'copyToClipboard',
  readFileBuffer: 'readFileBuffer',
  bufferCompression: 'bufferCompression',
  downloadFile: 'downloadFile',
  downloadScripts: 'downloadScripts',
  getBaseName: 'getBaseName',
  getCacheFilePath: 'getCacheFilePath',
  getCacheImageDir: 'getCacheImageDir',
  copyFileByDirectory: 'copyFileByDirectory',
  getOnlineScriptConfig: 'getOnlineScriptConfig',
  updateOnlineScriptConfig: 'updateOnlineScriptConfig',
  removeVideoFile: 'removeVideoFile',
  parsingURLContent: 'parsingURLContent',

  //获取未完成的任务数量
  getUnfinishedTaskIds: 'getUnfinishedTaskIds',
  //撤销发布任务
  cancelPublishTask: 'cancelPublishTask',
  //推送app任务
  pushAppTask: 'pushAppTask',
  //推送视频
  pushVideo: 'pushVideo',
  //推送视频进度
  pushVideoProgress: 'pushVideoProgress',
  //推送视频成功
  pushVideoSuccess: 'pushVideoSuccess',
  //推送视频失败
  pushVideoFailed: 'pushVideoFailed',
  //预约查询作品状态
  scheduleStateQuery: 'scheduleStateQuery',
  //查询作品状态开始
  stateQueryStart: 'stateQueryStart',
  auditStateQuery: 'auditStateQuery',
  //检测账号状态
  sessionDetect: 'sessionDetect',
  //获取账号统计数据
  queryAccountOverview: 'queryAccountOverview',
  //获取作品概览数据头
  queryPublishOverviewsHeaders: 'queryPublishOverviewsHeaders',
  //获取作品概览数据
  queryPublishOverviews: 'queryPublishOverviews',
  //检测账号状态完成
  sessionDetectFinished: 'sessionDetectFinished',

  setWindowSize: 'setWindowSize',
  setWindowMaxSize: 'setWindowMaxSize',
  setWindowMinSize: 'setWindowMinSize',
  setWindowCenter: 'setWindowCenter',
  setWindowResizable: 'setWindowResizable',

  exitConfirming: 'exitConfirming',
  exitConfirmed: 'exitConfirmed',
  businessContextChanged: 'businessContextChanged',
  autoUpdate: 'autoUpdate',
  checkOnlineVersion: 'checkOnlineVersion',

  platform: {
    douYin: {
      getTopics: 'platform.douYin.getTopics',
      getFriends: 'platform.douYin.getFriends',
      getLocations: 'platform.douYin.getLocations',
      getBringLocations: 'platform.douYin.getBringLocations',
      getMusicCategories: 'platform.douYin.getMusicCategories',
      checkBringLocations: 'platform.douYin.checkBringLocations',
      getMusic: 'platform.douYin.getMusic',
    },
    kuaiShou: {
      getTopics: 'platform.kuaiShou.getTopics',
      getFriends: 'platform.kuaiShou.getFriends',
      getLocations: 'platform.kuaiShou.getLocations',
      getMusic: 'platform.kuaiShou.getMusic',
    },
    biliBili: {
      getTopics: 'platform.biliBili.getTopics',
      getFriends: 'platform.biliBili.getFriends',
      getCategories: 'platform.biliBili.Categories',
    },
    xiaoHongShu: {
      getTopics: 'platform.xiaoHongShu.getTopics',
      getFriends: 'platform.xiaoHongShu.getFriends',
      getLocations: 'platform.xiaoHongShu.getLocations',
    },
    weiXinShiPinHao: {
      getTopics: 'platform.shiPinHao.getTopics',
      getFriends: 'platform.shiPinHao.getFriends',
      getLocations: 'platform.shiPinHao.getLocations',
      getMusics: 'platform.shiPinHao.getMusics',
    },
    baiJiaHao: {
      getTopics: 'platform.baiJiaHao.getTopics',
      getLocations: 'platform.baiJiaHao.getLocations',
      getCategories: 'platform.baiJiaHao.Categories',
    },
    tengXunWeiShi: {
      getTopics: 'platform.TengXunWeiShi.getTopics',
    },
    touTiaoHao: {
      getTopics: 'platform.touTiaoHao.getTopics',
    },
    xinLangWeiBo: {
      getTopics: 'platform.xinLangWeiBo.getTopics',
      getLocations: 'platform.xinLangWeiBo.getLocations',
    },
    zhiHu: {
      getTopics: 'platform.zhiHu.getTopics',
    },
  },
}

export const windowEvents = {
  maximum: 'window.maximum',
  minimum: 'window.minimum',
  close: 'window.close',
  move: 'window.move',
  focusWindow: 'window.focusWindow',
  sidebarCollapse: 'window.sidebarCollapse',
}
