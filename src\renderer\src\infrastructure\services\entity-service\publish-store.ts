import type { PublishStoreObject } from '@renderer/infrastructure/entity'
import type {
  PushingTask,
  PushingTaskSet,
  PushingTaskSetIdentifier,
} from '@renderer/infrastructure/model'
import { Publish } from '@renderer/infrastructure/model'
import type { PublishRepository } from '@renderer/infrastructure/repository/repositories'
import { usePublishRepository } from '@renderer/infrastructure/repository/repositories'
import { useMemo } from 'react'
import type { PushingTaskSetStore } from './pushing-task-set-store'
import { usePushingTaskSetStore } from './pushing-task-set-store'
import type { PushingTaskStore } from '@renderer/infrastructure/services/entity-service/pushing-task-store'
import { usePushingTaskStore } from '@renderer/infrastructure/services/entity-service/pushing-task-store'

function _Model2Object(publish: Publish) {
  const publishStoreList: PublishStoreObject[] = []
  for (const task of publish.tasks) {
    publishStoreList.push({
      taskSetId: publish.taskSet.taskSetId,
      taskId: task.taskId,
    } satisfies PublishStoreObject as PublishStoreObject)
  }

  return publishStoreList
}

function _Object2Model(taskSet: PushingTaskSet, tasks: PushingTask[]) {
  return new Publish(taskSet, tasks)
}

export function usePublishStore() {
  const pushingTaskRepository = usePublishRepository()
  const taskSetStore = usePushingTaskSetStore()
  const taskStore = usePushingTaskStore()
  return useMemo(
    () => new PublishStore(pushingTaskRepository, taskSetStore, taskStore),
    [pushingTaskRepository, taskSetStore, taskStore],
  )
}

export class PublishStore {
  constructor(
    private repository: PublishRepository,
    private taskSetStore: PushingTaskSetStore,
    private taskStore: PushingTaskStore,
  ) {}

  async save(publish: Publish) {
    return this.repository.bulkSave(_Model2Object(publish))
  }

  async getByTaskSetId(taskSetId: PushingTaskSetIdentifier) {
    const storeObjects = await this.repository.getMany((x) =>
      x.where('taskSetId').equals(taskSetId).toArray(),
    )
    const taskSet = await this.taskSetStore.get(taskSetId)
    const tasks = await this.taskStore.getMany(storeObjects.map((x) => x.taskId))
    return _Object2Model(taskSet, tasks)
  }

  async getIdsInSameSet(taskId: string) {
    const storeObject = await this.repository.getOne((x) =>
      x.where('taskId').equals(taskId).toArray(),
    )
    if (!storeObject) throw new Error('Publish not found')
    const storeObjects = await this.repository.getMany((x) =>
      x.where('taskSetId').equals(storeObject.taskSetId).toArray(),
    )
    return {
      taskSetId: storeObject.taskSetId,
      taskIds: storeObjects.map((x) => x.taskId),
    }
  }

  async getTaskIds(taskSetId: PushingTaskSetIdentifier | PushingTaskSetIdentifier[]) {
    if (Array.isArray(taskSetId)) {
      const storeObjects = await this.repository.getMany((x) =>
        x.where('taskSetId').anyOf(taskSetId).toArray(),
      )
      return storeObjects.map((x) => x.taskId)
    }
    const storeObjects = await this.repository.getMany((x) =>
      x.where('taskSetId').equals(taskSetId).toArray(),
    )
    return storeObjects.map((x) => x.taskId)
  }

  // 删除任务集下的所有发布
  async removeByTaskSetId(taskSetId: PushingTaskSetIdentifier | PushingTaskSetIdentifier[]) {
    if (Array.isArray(taskSetId)) {
      await this.repository.bulkDelete(taskSetId)
      return
    }
    await this.repository.delete(taskSetId)
  }

  async getTaskSetId(taskId: string) {
    const storeObject = await this.repository.getOne((x) =>
      x.where('taskId').equals(taskId).toArray(),
    )
    if (!storeObject) throw new Error('Publish not found')
    return storeObject.taskSetId
  }
}
