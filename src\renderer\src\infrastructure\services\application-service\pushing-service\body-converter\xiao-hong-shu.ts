import {
  platforms,
  type Account,
  type ImageFileInfo,
  type VideoContentViewModel,
  type VideoFileInfo,
} from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'

import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import { ignoreException } from './pushing-task-body-converter'
import { utils } from '@coozf/editor'
import type { AccountSession, PlatformDataItem, WithLocalStorage } from '@common/structure'
import type { XiaohongshuLocationInfo } from '@yixiaoer/platform-service'
import { xiaoHongShuPlatformService } from '@renderer/infrastructure/services'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type {
  XiaohongshuDynamicTaskInput,
  XiaohongshuVideoTaskInput,
} from '@yixiaoer/platform-service/dist/media-platform/xiaohongshu'
import type {
  LocalImageTextPushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'

class XiaoHongShuPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    // 转换话题
    description = await this.getDescription(description, session)
    // 获取地理位置
    const location: PlatformDataItem<XiaohongshuLocationInfo> | undefined = await this.getLocation(
      locationKeyword,
      pushingConfig.location,
      account,
      session,
    )

    return {
      localStorage: session.localStorage,
      pubType: toDraft ? 0 : 1,
      taskId: taskId,
      video: {
        duration: video.fileDurationTimeSpan.seconds,
        width: video.videoWidth,
        height: video.videoHeight,
        size: video.fileSize,
        localPath: video.filePath,
      },
      cover: {
        width: cover.width,
        height: cover.height,
        size: cover.size,
        pathOrUrl: cover.path,
      },
      desc: description,
      title: title,
      topics: undefined,
      mentionedUser: undefined,
      postTime: timing,
      location: location?.raw,
      visibilityType: toDraft ? 1 : 0,
    } satisfies XiaohongshuVideoTaskInput & WithLocalStorage
  }

  async toDynamic(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalImageTextPushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    // 转换话题
    const description = await this.getDescription(config.description, session)
    // 获取地理位置
    const location = await this.getLocation(
      config.locationKeyword,
      config.location,
      account,
      session,
    )
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      images: config.images.map((image) => ({
        width: image.width,
        height: image.height,
        size: image.size,
        pathOrUrl: image.path,
      })),
      desc: description,
      title: config.title,
      topics: undefined,
      mentionedUser: undefined,
      postTime: config.timing,
      location: location?.raw,
      visibilityType: toDraft ? 1 : 0,
    } satisfies XiaohongshuDynamicTaskInput & WithLocalStorage
  }

  private async getLocation(
    locationKeyword: string,
    configLocation: VideoContentViewModel['location'] | null,
    account: Account,
    session: AccountSession,
  ) {
    if (configLocation && configLocation[platforms.XiaoHongShu.name]) {
      return configLocation[platforms.XiaoHongShu.name] as PlatformDataItem<XiaohongshuLocationInfo>
    }
    let location: PlatformDataItem<XiaohongshuLocationInfo> | undefined
    await ignoreException(async () => {
      location = locationKeyword
        ? (await xiaoHongShuPlatformService.getLocations(account, session, locationKeyword))[0] ??
          undefined
        : undefined
    })
    return location
  }

  private async getDescription(description: string, session: AccountSession) {
    return await utils.commonTopic.modifyTopics(description, async (element) => {
      await ignoreException(async () => {
        const keyword = element.getAttribute('text')
        if (!keyword) return

        const topic = (await xiaoHongShuPlatformService.getTopics(session, keyword))[0] ?? undefined
        if (!topic) element.remove()
        else element.setAttribute('raw', JSON.stringify(topic.raw))
      })
    })
  }
}

export const xiaoHongShu = new XiaoHongShuPushingTaskBodyConverter()
