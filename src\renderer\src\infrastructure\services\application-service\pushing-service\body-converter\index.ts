import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { Account } from '@renderer/infrastructure/model'
import { platforms } from '@renderer/infrastructure/model'
import { douYin } from './dou-yin'
import { kuaiShou } from './kuai-shou'
import { weiXinShiPinHao } from './wei-xin-shi-pin-hao'
import { xiaoHongShu } from './xiao-hong-shu'
import { bilibili } from './bili-bili'
import { baiJiaHao } from './bai-jia-hao'
import { touTiaoHao } from './tou-tiao-hao'
import { xinLangWeiBo } from './xin-lang-wei-bo'
import { zhiHu } from './zhi-hu'
import { qiEHao } from './qi-e-hao'
import { souHuHao } from './sou-hu-hao'
import { yiDianHao } from './yi-dian-hao'
import { daYuHao } from './da-yu-hao'
import { wangYiHao } from './wang-yi-hao'
import { aiQiYi } from './ai-qi-yi'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import type { AccountSession } from '@common/structure'
import type { LocalPushingConfig } from '@renderer/infrastructure/model/config/pushing-config'
import {
  LocalArticlePushingConfig,
  LocalImageTextPushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { PushContentType } from '@common/model/content-type'
import { platformNames } from '@common/model/platform-name'
import { tengXunWeiShi } from '@renderer/infrastructure/services/application-service/pushing-service/body-converter/teng-xun-wei-shi'

const converters: { [platformName: string]: PushingTaskBodyConverter } = {
  [platforms.DouYin.name]: douYin,
  [platforms.KuaiShou.name]: kuaiShou,
  [platforms.WeiXinShiPinHao.name]: weiXinShiPinHao,
  [platforms.XiaoHongShu.name]: xiaoHongShu,
  [platforms.Bilibili.name]: bilibili,
  [platforms.BaiJiaHao.name]: baiJiaHao,
  [platforms.TouTiaoHao.name]: touTiaoHao,
  [platforms.XinLangWeiBo.name]: xinLangWeiBo,
  [platforms.ZhiHu.name]: zhiHu,
  [platformNames.QiEHao]: qiEHao,
  [platformNames.SouHuHao]: souHuHao,
  [platformNames.YiDianHao]: yiDianHao,
  [platformNames.DaYuHao]: daYuHao,
  [platformNames.WangYiHao]: wangYiHao,
  [platformNames.AiQiYi]: aiQiYi,
  [platformNames.TengXunWeiShi]: tengXunWeiShi,
}

export const bodyConverter = {
  convert: async (
    taskId: string,
    platformName: string,
    contentType: PushContentType,
    account: Account,
    session: AccountSession,
    pushingConfig: LocalPushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> => {
    if (pushingConfig instanceof LocalVideoPushingConfig) {
      if (converters[platformName].toVideo) {
        return await converters[platformName].toVideo(
          taskId,
          account,
          session,
          contentType,
          pushingConfig,
          pushingConfig.video!,
          pushingConfig.cover!,
          pushingConfig.title,
          pushingConfig.description,
          pushingConfig.isOriginal,
          pushingConfig.locationKeyword,
          toDraft,
          pushingConfig.timing,
        )
      } else {
        throw new Error(`平台${platformName}不支持视频内容body转换`)
      }
    } else if (pushingConfig instanceof LocalImageTextPushingConfig) {
      if (converters[platformName].toDynamic) {
        return await converters[platformName].toDynamic(
          taskId,
          account,
          session,
          contentType,
          pushingConfig,
          toDraft,
        )
      } else {
        throw new Error(`平台${platformName}不支持图文内容body转换`)
      }
    } else if (pushingConfig instanceof LocalArticlePushingConfig) {
      if (converters[platformName].toArticle) {
        return await converters[platformName].toArticle(
          taskId,
          account,
          session,
          contentType,
          pushingConfig,
          toDraft,
        )
      } else {
        throw new Error(`平台${platformName}不支持文章内容body转换`)
      }
    } else {
      throw new Error('Unknown pushing config type')
    }
  },
}
