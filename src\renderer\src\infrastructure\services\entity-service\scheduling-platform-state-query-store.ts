import type { SchedulingPlatformStateQueryRepository } from '../../repository/repositories'
import { useSchedulingPlatformStateQueryRepository } from '../../repository/repositories'
import { SchedulingPlatformStateQueryTask } from '@renderer/infrastructure/model'
import type { SchedulingPlatformStateQueryStoreObject } from '../../entity'
import { useMemo } from 'react'

export function useSchedulingPlatformStateQueryStore() {
  const repository = useSchedulingPlatformStateQueryRepository()
  return useMemo(() => new SchedulingPlatformStateQueryStore(repository), [repository])
}

export class SchedulingPlatformStateQueryStore {
  constructor(
    private schedulingPlatformStateQueryRepository: SchedulingPlatformStateQueryRepository,
  ) {}

  private _Model2Entity(model: SchedulingPlatformStateQueryTask) {
    return {
      taskId: model.taskId,
      scheduleDatetime: model.scheduleDatetime,
    } satisfies SchedulingPlatformStateQueryStoreObject as SchedulingPlatformStateQueryStoreObject
  }

  private _Object2Model(object: SchedulingPlatformStateQueryStoreObject) {
    return new SchedulingPlatformStateQueryTask(object.taskId, object.scheduleDatetime)
  }

  save(schedulingPlatformStateQueryTask: SchedulingPlatformStateQueryTask) {
    return this.schedulingPlatformStateQueryRepository.save(
      this._Model2Entity(schedulingPlatformStateQueryTask),
    )
  }

  async delete(taskId: string | string[]) {
    if (Array.isArray(taskId)) {
      return this.schedulingPlatformStateQueryRepository.bulkDelete(taskId)
    }
    return this.schedulingPlatformStateQueryRepository.delete(taskId)
  }

  async getAll() {
    const storeObjects = await this.schedulingPlatformStateQueryRepository.getMany()
    return storeObjects.map(this._Object2Model)
  }
}
