import type { Account, ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import { ignoreException } from './pushing-task-body-converter'
import type { AccountSession, WithLocalStorage } from '@common/structure'
import type { ZhiHuVideoTaskInput } from '@yixiaoer/platform-service'
import { utils } from '@coozf/editor'
import { datetimeService, zhiHuPlatformService } from '@renderer/infrastructure/services'
import type {
  LocalArticlePushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { ZhihuArticleTaskInput } from '@yixiaoer/platform-service/dist/media-platform/zhihu/zhihu-article'
import { platformNames } from '@common/model/platform-name'
import type { ZhihuTopicInfo } from '@yixiaoer/platform-service/dist/media-platform/zhihu/zhihu-topic.service'

async function getDescription(description: string, session: AccountSession) {
  return await utils.commonTopic.modifyTopics(description, async (element) => {
    await ignoreException(async () => {
      const keyword = element.getAttribute('text')
      if (!keyword) return

      const topic = (await zhiHuPlatformService.getTopics(session, keyword))[0] ?? undefined
      if (!topic) element.remove()
      else element.setAttribute('raw', JSON.stringify(topic.raw))
    })
  })
}

class ZhiHuPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    description = await getDescription(description, session)

    const platformConfig = pushingConfig.platformConfigs[platformNames.ZhiHu]
    const categories = platformConfig.categories
    if (!categories) {
      throw new Error('知乎视频分类不能为空')
    }

    const topics: ZhihuTopicInfo[] = []
    for (const tag of pushingConfig.tags) {
      const topic = (await zhiHuPlatformService.getTopics(session, tag))[0] ?? undefined
      if (topic) {
        topics.push(topic.raw)
      }
    }

    return {
      localStorage: session.localStorage,
      taskId: taskId,
      video: {
        duration: video.fileDurationTimeSpan.seconds,
        width: video.videoWidth,
        height: video.videoHeight,
        size: video.fileSize,
        localPath: video.filePath,
      },
      cover: {
        width: cover.width,
        height: cover.height,
        size: cover.size,
        pathOrUrl: cover.path,
      },
      desc: description,
      title: title,
      pubType: toDraft ? 0 : 1,
      /**
       * 话题
       */
      topics: topics,
      /**
       * 定时发布，10位时间戳
       */
      postTime: datetimeService.toSeconds(timing),
      /**
       * 一级分类
       */
      firstLevel: categories[0].raw.data,
      /**
       * 二级分类
       */
      secondLevel: categories[1].raw.data,
      /**
       * 是否原创 1:原创，0:转载
       */
      isOriginal: isOriginal ? 1 : 0,
      /**
       * 活动
       */
      activity: undefined,
    } satisfies ZhiHuVideoTaskInput & WithLocalStorage
  }

  async toArticle(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalArticlePushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    const platformConfig = config.platformConfigs[platformNames.ZhiHu]
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      pubType: toDraft ? 0 : 1,
      title: config.title,
      covers: config.cover
        ? [
            {
              width: config.cover.width,
              height: config.cover.height,
              size: config.cover.size,
              pathOrUrl: config.cover.path,
            },
          ]
        : [],
      content: config.content,
      topics: platformConfig.topic.map((topic) => topic.raw),
    } satisfies ZhihuArticleTaskInput & WithLocalStorage
  }
}

export const zhiHu = new ZhiHuPushingTaskBodyConverter()
