import type {
  Account,
  ImageFileInfo,
  VideoContentViewModel,
  VideoFileInfo,
} from '@renderer/infrastructure/model'
import { platforms } from '@renderer/infrastructure/model'
import type {
  KuaishouDynamicTaskInput,
  KuaishouLocationInfo,
  KuaishouMusicInfo,
} from '@yixiaoer/platform-service'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import { ignoreException } from './pushing-task-body-converter'
import { utils } from '@coozf/editor'
import type { AccountSession, MusicPlatformDataItem, PlatformDataItem } from '@common/structure'
import { kuaiShouPlatformService } from '@renderer/infrastructure/services'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { KuaishouVideoTaskInput } from '@yixiaoer/platform-service/dist/media-platform/kuaishou/kuaishou-video.service'
import type { WithLocalStorage } from '@common/structure/with-local-storage'
import type {
  LocalImageTextPushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { PushContentType } from '@common/model/content-type'

class KuaiShouPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    // 转换话题
    description = await this.getDescription(description, session)
    // 获取地理位置

    const location = await this.getLocation(
      locationKeyword,
      pushingConfig.location,
      account,
      session,
    )
    return {
      localStorage: session.localStorage,
      pubType: 1,
      taskId: taskId,
      video: {
        duration: video.fileDurationTimeSpan.seconds,
        width: video.videoWidth,
        height: video.videoHeight,
        size: video.fileSize,
        localPath: video.filePath,
      },
      cover: {
        width: cover.width,
        height: cover.height,
        size: cover.size,
        pathOrUrl: cover.path,
      },
      desc: description,
      topics: [],
      mentionedUser: [],
      domain: undefined,
      secondDomain: undefined,
      publishTime: timing,
      location: location?.raw,
      downloadType: undefined,
      disableNearbyShow: undefined,
      allowSameFrame: undefined,
      visibilityType: toDraft ? 2 : 1,
      goods: undefined,
    } satisfies KuaishouVideoTaskInput & WithLocalStorage
  }

  async toDynamic(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalImageTextPushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    // 转换话题
    const description = await this.getDescription(config.description, session)
    // 获取地理位置
    const location = await this.getLocation(
      config.locationKeyword,
      config.location,
      account,
      session,
    )
    const music = config.music?.[
      platforms.KuaiShou.name
    ] as MusicPlatformDataItem<KuaishouMusicInfo>
    return {
      localStorage: session.localStorage,
      pubType: 1,
      taskId: taskId,
      cover: {
        width: config.cover.width,
        height: config.cover.height,
        size: config.cover.size,
        pathOrUrl: config.cover.path,
      },
      images: config.images.map((image) => ({
        width: image.width,
        height: image.height,
        size: image.size,
        pathOrUrl: image.path,
      })),
      desc: description,
      topics: [],
      mentionedUser: [],
      location: location?.raw,
      visibilityType: toDraft ? 2 : 1,
      music: music?.raw,
      goods: undefined,
      publishTime: config.timing,
    } satisfies KuaishouDynamicTaskInput & WithLocalStorage
  }

  private async getLocation(
    locationKeyword: string,
    configLocation: VideoContentViewModel['location'] | null,
    account: Account,
    session: AccountSession,
  ) {
    if (configLocation && configLocation[platforms.KuaiShou.name]) {
      return configLocation[platforms.KuaiShou.name] as PlatformDataItem<KuaishouLocationInfo>
    }
    let location: PlatformDataItem<KuaishouLocationInfo> | undefined
    await ignoreException(async () => {
      location = locationKeyword
        ? (await kuaiShouPlatformService.getLocations(account, session, locationKeyword))[0] ??
          undefined
        : undefined
    })
    return location
  }
  private getDescription = async (description: string, session: AccountSession) => {
    return await utils.commonTopic.modifyTopics(description, async (element) => {
      await ignoreException(async () => {
        const keyword = element.getAttribute('text')
        if (!keyword) return

        const topic = (await kuaiShouPlatformService.getTopics(session, keyword))[0] ?? undefined
        if (!topic) element.remove()
        else element.setAttribute('raw', JSON.stringify(topic.raw))
      })
    })
  }
}

export const kuaiShou = new KuaiShouPushingTaskBodyConverter()
