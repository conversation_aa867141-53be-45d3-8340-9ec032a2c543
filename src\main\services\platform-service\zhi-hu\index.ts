import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type { PlatformDataItem } from '@common/structure'
import type { ZhiHuVideoTaskInput } from '@yixiaoer/platform-service'
import type { ZhihuArticleTaskInput } from '@yixiaoer/platform-service/dist/media-platform/zhihu/zhihu-article'
import { Platforms } from '@yixiaoer/platform-service'

class ZhiHuPlatformService extends PlatformService {
  constructor() {
    super(platformNames.ZhiHu)
  }
  async pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)

    const result = await this.getPushingResult(async (eventEmitter) => {
      return (await getPlatformServicePromise()).Zhihu.publishVideo(
        cookie,
        body as ZhiHuVideoTaskInput,
        eventEmitter,
      )
    }, progressCallBack)

    return result.publishId!
  }

  async pushArticle(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)

    const result = await this.getPushingResult(async (eventEmitter) => {
      return (await getPlatformServicePromise()).Zhihu.publishArticle(
        cookie,
        body as ZhihuArticleTaskInput,
        eventEmitter,
      )
    }, progressCallBack)

    return result.publishId!
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Zhihu.getZhihuUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(platformNames.ZhiHu, info.id, info.name, info.avatar_url)
  }

  async getTopics(cookies: Electron.Cookie[], keyword: string) {
    const cookie = this.convertCookie(cookies)
    const data = await this.getData(
      async () => (await getPlatformServicePromise()).Zhihu.getZhihuTopicList(cookie, keyword),
      (x) => x.data!,
    )
    return data.map((x) => ({ id: x.id, text: x.name, raw: x }) satisfies PlatformDataItem)
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    // 知乎没有子类型配置，只获取一种数据类型放到 video 字段中
    const data = await platformService.DataService.getAccountReport(Platforms.ZhiHu, cookie)

    return {
      video: data.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    return await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(Platforms.ZhiHu, false, cookie),
      (x) => x.data || [],
    )
  }
}

export const zhiHuPlatformService = new ZhiHuPlatformService()
