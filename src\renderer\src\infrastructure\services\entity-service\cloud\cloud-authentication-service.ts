import { useAnonymousApiService } from '@renderer/infrastructure/services/network-service/anonymous-api-service'
import { deviceId } from '@renderer/infrastructure/utils/version'
import { useMemo } from 'react'

export function useCloudAuthenticationService() {
  const anonymousApiService = useAnonymousApiService()
  return useMemo(() => new CloudAuthenticationService(anonymousApiService), [anonymousApiService])
}

export class CloudAuthenticationService {
  constructor(public anonymousApiService: ReturnType<typeof useAnonymousApiService>) {}

  sendVerifyCode(phone: string) {
    return this.anonymousApiService.post<string>(`/users/sms-code`, {
      phone: phone,
      sence: 'auth',
    })
  }

  async authentic(params: { username: string; code?: string; password?: string }) {
    return this.anonymousApiService.post<{
      authorization: string
    }>('/users/auth', { ...params, deviceId: deviceId })
  }
}
