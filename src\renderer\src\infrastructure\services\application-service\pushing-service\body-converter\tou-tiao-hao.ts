import type { Account, ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import { PushContentType } from '@common/model/content-type'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import { ignoreException } from './pushing-task-body-converter'
import type {
  ToutiaohaoArticleTaskInput,
  ToutiaohaoMiniVideoTaskInput,
  ToutiaohaoVideoTaskInput,
} from '@yixiaoer/platform-service/dist/media-platform/toutiaohao'
import type { AccountSession, WithLocalStorage } from '@common/structure'
import { utils } from '@coozf/editor'
import { datetimeService, touTiaoHaoPlatformService } from '@renderer/infrastructure/services'
import type {
  LocalArticlePushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'

function toVerticalVideo(
  taskId: string,
  account: Account,
  session: AccountSession,
  video: VideoFileInfo,
  cover: ImageFileInfo,
  title: string,
  timing?: number,
) {
  return {
    localStorage: session.localStorage,
    taskId: taskId,
    video: {
      duration: video.fileDurationTimeSpan.seconds,
      width: video.videoWidth,
      height: video.videoHeight,
      size: video.fileSize,
      localPath: video.filePath,
    },
    cover: {
      width: cover.width,
      height: cover.height,
      size: cover.size,
      pathOrUrl: cover.path,
    },
    title: title,
  } satisfies ToutiaohaoMiniVideoTaskInput & WithLocalStorage
}

async function toHorizonVideo(
  taskId: string,
  account: Account,
  session: AccountSession,
  video: VideoFileInfo,
  cover: ImageFileInfo,
  title: string,
  description: string,
  isOriginal: boolean,
  toDraft: boolean,
  timing?: number,
) {
  // 转换话题
  description = await utils.commonTopic.modifyTopics(description, async (element) => {
    await ignoreException(async () => {
      const keyword = element.getAttribute('text')
      if (!keyword) return

      const topic = (await touTiaoHaoPlatformService.getTopics(session, keyword))[0] ?? undefined
      if (!topic) element.remove()
      else element.setAttribute('raw', JSON.stringify(topic.raw))
    })
  })

  return {
    localStorage: session.localStorage,
    taskId: taskId,
    video: {
      duration: video.fileDurationTimeSpan.seconds,
      width: video.videoWidth,
      height: video.videoHeight,
      size: video.fileSize,
      localPath: video.filePath,
    },
    cover: {
      width: cover.width,
      height: cover.height,
      size: cover.size,
      pathOrUrl: cover.path,
    },
    title: title,
    pubType: toDraft ? 0 : 1,
    original: isOriginal,
    visibilityType: toDraft ? 1 : 0,
    desc: description,
    publishTime: timing ? datetimeService.format(timing, 'yyyy-MM-dd HH:mm:ss') : undefined,
  } satisfies ToutiaohaoVideoTaskInput & WithLocalStorage
}

class TouTiaoHaoPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    if (contentType === PushContentType.VerticalVideo)
      return toVerticalVideo(taskId, account, session, video, cover, title, timing)
    else
      return toHorizonVideo(
        taskId,
        account,
        session,
        video,
        cover,
        title,
        description,
        isOriginal,
        toDraft,
        timing,
      )
  }

  async toArticle(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalArticlePushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      pubType: toDraft ? 0 : 1,
      title: config.title,
      covers: [
        {
          width: config.cover!.width,
          height: config.cover!.height,
          size: config.cover!.size,
          pathOrUrl: config.cover!.path,
        },
      ],
      content: config.content,
      adType: 0,
      location: {
        city: config.locationKeyword,
        city_code: '',
      },
      claimExclusive: config.isFirst ? 1 : 0,
      cancelToken: undefined,
      publishTime: config.timing
        ? datetimeService.format(config.timing, 'yyyy-MM-dd HH:mm:ss')
        : undefined,
    } satisfies ToutiaohaoArticleTaskInput & WithLocalStorage
  }
}

export const touTiaoHao = new TouTiaoHaoPushingTaskBodyConverter()
