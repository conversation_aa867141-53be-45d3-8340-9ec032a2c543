import type { EditContentType } from '@common/model/content-type'
import type { Brand, Identifier } from '@renderer/infrastructure/types/brand'
import { immerable } from 'immer'
import type { TaskState } from '@renderer/infrastructure/model/pushing-task/task-state'

export type PushingTaskSetIdentifier = Brand<'PushingTaskSetIdentifier', Identifier>

export type PushingTaskSetState = '发布中' | '全部发布成功' | '全部发布失败' | '部分发布成功'

export function getPushingTaskSetState(taskStates: TaskState[]): PushingTaskSetState {
  if (taskStates.every((taskState) => taskState === '成功')) {
    return '全部发布成功'
  } else if (taskStates.every((taskState) => taskState === '失败')) {
    return '全部发布失败'
  } else if (taskStates.some((taskState) => taskState === '成功')) {
    return '部分发布成功'
  } else {
    return '发布中'
  }
}

/**
 * 一次发布任务集简介
 */
export class PushingTaskSet {
  [immerable] = true

  constructor(
    public readonly taskSetId: PushingTaskSetIdentifier,
    public readonly platformNames: string[],
    public readonly createTime: Date,
  ) {}

  public fromApp: boolean = false
  public state: PushingTaskSetState = '发布中'
  public contentType!: EditContentType
  public thumbUrl!: string
  public brief!: string
  public hasFailedTask: boolean = false

  get finished() {
    return this.state !== '发布中'
  }

  static fromStore(
    taskSetId: PushingTaskSetIdentifier,
    contentTypeByName: EditContentType,
    platformNames: string[],
    thumbUrl: string,
    brief: string,
    createTime: Date,
    state: PushingTaskSetState,
    fromApp: boolean,
    hasFailedTask: boolean,
  ) {
    const taskSet = new PushingTaskSet(taskSetId, platformNames, createTime)
    taskSet.fromApp = fromApp
    taskSet.state = state
    taskSet.contentType = contentTypeByName
    taskSet.thumbUrl = thumbUrl
    taskSet.brief = brief
    taskSet.hasFailedTask = hasFailedTask
    return taskSet
  }

  static fromLocalPush(
    taskSetId: PushingTaskSetIdentifier,
    platformNames: string[],
    createTime: Date,
  ) {
    const pushingTaskSet = new PushingTaskSet(taskSetId, platformNames, createTime)
    pushingTaskSet.fromApp = false
    return pushingTaskSet
  }

  static fromAppPush(
    taskSetId: PushingTaskSetIdentifier,
    platformNames: string[],
    createTime: Date,
  ) {
    const pushingTaskSet = new PushingTaskSet(taskSetId, platformNames, createTime)
    pushingTaskSet.fromApp = true
    return pushingTaskSet
  }

  updateState(taskStates: TaskState[]) {
    this.state = getPushingTaskSetState(taskStates)
    this.hasFailedTask = taskStates.some((taskState) => taskState === '失败')
  }
}
