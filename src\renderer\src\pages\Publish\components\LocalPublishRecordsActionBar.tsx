import { PushStateFilter } from './PushStateFilter'
import type { PublishRecordsFilter } from '@renderer/pages/Publish/types/publish-records-filter'
import { ContentTypeFilter } from './ContentTypeFilter'

import { NewPublishHoverCard } from '@renderer/pages/Publish/new-publish-hover-card'
import { MultipleOperation } from '../MultipleOperation'
import { usePublishOperationStore } from '@renderer/store/publishOperationStore'

interface TopActionBarProps {
  filter: PublishRecordsFilter
  setFilter: (update: (draft: PublishRecordsFilter) => void) => void
}

export function LocalPublishRecordsActionBar({ filter, setFilter }: TopActionBarProps) {
  const [isSelectMode] = usePublishOperationStore((state) => [state.isSelectMode])
  return (
    <div className="flex items-center">
      <div className="flex flex-grow gap-3">
        <div className="w-36">
          <ContentTypeFilter
            value={filter.contentType}
            onChange={(value) => {
              setFilter((x) => {
                x.contentType = value
              })
            }}
          />
        </div>
        <div className="w-36">
          <PushStateFilter
            value={filter.pushState}
            onChange={(value) => {
              setFilter((x) => {
                x.pushState = value
              })
            }}
          />
        </div>{' '}
        <div className={isSelectMode ? 'grow' : ''}></div>
        <div className="flex flex-shrink-0 gap-1">
          <MultipleOperation isLocal />
        </div>
      </div>

      {!isSelectMode && (
        <div className="flex-shrink">
          <NewPublishHoverCard></NewPublishHoverCard>
        </div>
      )}
    </div>
  )
}
