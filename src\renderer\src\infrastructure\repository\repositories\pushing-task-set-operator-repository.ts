import { Repository } from '../index'
import type { PushingTaskSetOperatorStoreObject } from '../../entity'
import type { MemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemo } from 'react'

export function usePushingTaskSetOperatorRepository() {
  const database = useMemberDatabase()
  return useMemo(() => new PushingTaskSetOperatorRepository(database), [database])
}

export class PushingTaskSetOperatorRepository extends Repository<
  PushingTaskSetOperatorStoreObject,
  'taskSetId'
> {
  constructor(database: MemberDatabase) {
    super(database.instance.PushingTaskSetOperator)
  }
}
