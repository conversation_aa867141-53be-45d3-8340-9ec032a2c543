import { Popover, PopoverContent, PopoverTrigger } from '@renderer/shadcn-components/ui/popover'
import { Input } from '@renderer/shadcn-components/ui/input'
import CloseIcon from '@browser/assets/close.svg?react'
import { Button } from '@renderer/shadcn-components/ui/button'
import type { ReactNode } from 'react'
import { useEffect, useState } from 'react'
import type { BrowserTabItem } from '@common/structure/browser/header'
import { useBrowserService } from '@browser/infrastructure/services/browser-service'
import { useFavoriteStore } from '@browser/store/favoriteStore'
import type { ContextFavorite } from '@common/structure/space/context-favorite'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@renderer/shadcn-components/ui/tooltip'

export function AddToFavorites({
  children,
  tab,
  favorite,
}: {
  children: ReactNode
  tab: BrowserTabItem
  favorite: ContextFavorite | null
}) {
  const [open, setOpen] = useState(false)
  const [name, setName] = useState('')
  const browserService = useBrowserService()
  const { isInFavorite, canAddFavorite } = useFavoriteStore()

  useEffect(() => {
    if (open) {
      setName(favorite?.name ?? tab.title)
    }
  }, [favorite?.name, open, tab.title])

  useEffect(() => {
    if (open) {
      browserService.setHeaderViewTop()
    } else {
      browserService.setTabViewTop()
    }
  }, [browserService, open])

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger onClick={() => browserService.setHeaderViewTop()}>
          {children}
        </PopoverTrigger>
        <PopoverContent className="mr-6 w-[400px] p-6">
          <div className="grid gap-4 space-y-5">
            <div className="flex items-center">
              <h4 className="flex-grow pl-1 font-semibold leading-none">收藏</h4>
              <CloseIcon
                className="cursor-pointer"
                onClick={() => {
                  setOpen(false)
                }}
              ></CloseIcon>
            </div>
            <div className="grid gap-2">
              <div className="flex">
                <Input
                  id="name"
                  className="col-span-2 grow"
                  value={name}
                  onChange={(e) => {
                    setName(e.target.value)
                  }}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              {isInFavorite(tab.contextIdentifier.contextId, tab.url) && (
                <Button
                  variant="outline"
                  onClick={async () => {
                    setOpen(false)
                    await browserService.removeFavorite(tab.contextIdentifier, tab.url)
                  }}
                >
                  移除收藏
                </Button>
              )}
              <TooltipProvider>
                <Tooltip delayDuration={0}>
                  <TooltipTrigger asChild>
                    <div>
                      <Button
                        onClick={async () => {
                          setOpen(false)
                          await browserService.saveFavorite(tab.contextIdentifier, name, tab.url)
                        }}
                        disabled={
                          !name ||
                          (!isInFavorite(tab.contextIdentifier.contextId, tab.url) &&
                            !canAddFavorite(tab.contextIdentifier.contextId))
                        }
                      >
                        完成
                      </Button>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent
                    hidden={
                      isInFavorite(tab.contextIdentifier.contextId, tab.url) ||
                      canAddFavorite(tab.contextIdentifier.contextId)
                    }
                  >
                    最多保存10个收藏
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </>
  )
}
