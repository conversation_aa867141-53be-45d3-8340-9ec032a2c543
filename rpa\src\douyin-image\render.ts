import type { IImageTask } from '../type'
import {
  getElementByText,
  getFileBuffer,
  manualDispatchFileEvent,
  onSendInput,
  retryCount,
  uint8ArrayToFile,
  baseStartPush,
  wait,
} from '../renderUtils'

const startPush = baseStartPush

const renderTaskMap = {
  image: 0,
  title: 0,
  description: 0,
  music: 0,
}

async function renderImage(config: IImageTask) {
  if (renderTaskMap.image < retryCount) {
    const inputDom = document.querySelector(
      '.semi-tabs-pane-motion-overlay input[accept="image/png,image/jpeg,image/jpg,image/bmp,image/webp,image/tif"][multiple]',
    )

    if (inputDom && inputDom instanceof HTMLInputElement) {
      const files = new DataTransfer()

      for (let i = 0; i < config.images.length; i++) {
        const uint8Array = await getFileBuffer(config.images[i].path)
        if (uint8Array) {
          const file = uint8ArrayToFile(
            uint8Array,
            `${config.title}_${Math.random()}.${config.images[i].format.toLocaleLowerCase()}`,
            `image/${config.images[i].format.toLocaleLowerCase()}`,
          )

          files.items.add(file)
        }
      }

      manualDispatchFileEvent({
        dom: inputDom,
        element: HTMLInputElement,
        elementKey: 'files',
        value: files.files,
        event: 'change',
      })

      renderTaskMap.image = retryCount
      await wait(3000)
    }

    renderTaskMap.image++
  }
}

async function render(config: IImageTask) {
  if (renderTaskMap.image < retryCount) {
    return
  }

  if (renderTaskMap.title < retryCount) {
    const inputDom = document.querySelector('.semi-input-wrapper input[placeholder="添加作品标题"]')
    if (inputDom && inputDom instanceof HTMLInputElement) {
      manualDispatchFileEvent({
        dom: inputDom,
        element: HTMLInputElement,
        elementKey: 'value',
        value: config.title,
        event: 'change',
      })

      renderTaskMap.title = retryCount
    }

    renderTaskMap.title++
  }

  if (renderTaskMap.description < retryCount) {
    const divDom = document.querySelector('.editor.editor-comp-publish')

    if (divDom && divDom instanceof HTMLDivElement) {
      await onSendInput(divDom, config.description, config.tabId)

      renderTaskMap.description = retryCount
    }

    renderTaskMap.description++
  }

  if (renderTaskMap.music < retryCount) {
    if (config.music) {
      const spanDom = getElementByText('span', '选择音乐')
      if (spanDom && spanDom instanceof HTMLSpanElement) {
        spanDom.click()
        await wait(300)

        const inputDom = document.querySelector('input[placeholder="搜索音乐"]')

        if (inputDom && inputDom instanceof HTMLInputElement) {
          manualDispatchFileEvent({
            dom: inputDom,
            element: HTMLInputElement,
            elementKey: 'value',
            value: config.music,
            event: 'change',
          })

          await wait(1000)

          let count = 0
          while (count < 10) {
            const listDom = document.querySelector('[class^="audio-collection-container-"]')
            if (listDom && listDom instanceof HTMLDivElement) {
              const items = listDom.querySelectorAll('[class^="card-container-"]')

              if (items && items.length) {
                for (const item of items) {
                  const titleDom = item.querySelector('[class^="audio-name-"]')
                  if (titleDom && titleDom instanceof HTMLDivElement) {
                    if (titleDom.textContent === config.music) {
                      const spanDom = getElementByText('span', '使用', item as HTMLElement)
                      if (
                        spanDom &&
                        spanDom instanceof HTMLSpanElement &&
                        spanDom.parentElement instanceof HTMLButtonElement
                      ) {
                        spanDom.parentElement.click()

                        break
                      }
                    }
                  }
                }
              }
            }

            count++
            await wait(1000)
          }

          renderTaskMap.music = retryCount
        }
      }
    } else {
      renderTaskMap.music = retryCount
    }

    renderTaskMap.music++
  }

  const taskList = Object.keys(renderTaskMap).filter((key) => renderTaskMap[key] < retryCount)
  if (!taskList.length) {
    isDone = true
  }
}
