import { usePushingService } from '@renderer/infrastructure/services'
import { useApiMutation } from './useApiQuery'
import { toast } from 'sonner'
import type { PushingTaskSetIdentifier } from '@renderer/infrastructure/model'
import { useQueryClient } from '@tanstack/react-query'

export const usePublishDelete = () => {
  const deleteRecordMutation = useApiMutation((id: PushingTaskSetIdentifier[]) => ({
    url: `/taskSets/batch`,
    method: 'delete',
    params: { taskSetIds: id },
  }))
  const { deletePushingTask } = usePushingService()

  const queryClient = useQueryClient()

  const onDelete = async (ids: PushingTaskSetIdentifier[]) => {
    toast.promise(
      async () => {
        await deleteRecordMutation.mutateAsync(ids)
        await queryClient.invalidateQueries({
          queryKey: ['cloudRecords'],
        })
        // 尝试删除本地任务，后续可能会废弃
        void deletePushingTask(ids)
      },
      {
        loading: '删除中...',
        success: '删除成功',
      },
    )
  }

  return {
    onDelete,
    deleteRecordMutation,
    queryClient,
    deletePushingTask,
  }
}
