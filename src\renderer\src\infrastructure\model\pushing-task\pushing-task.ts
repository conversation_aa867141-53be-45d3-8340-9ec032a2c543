import type { Platform } from '../platforms'
import type { EditContentType, PushContentType } from '@common/model/content-type'
import type { Brand, Identifier, TimeStamp } from '@renderer/infrastructure/types/brand'

export type PushingTaskIdentifier = Brand<'PushingTaskIdentifier', Identifier>

export class PushingTask {
  constructor(
    //TODO: 应该改为PushingTaskIdentifier类型来强化类型系统
    public taskId: string,
    public platform: Platform,
    public accountId: string,
    public editContentType: EditContentType,
    public pushContentType: PushContentType,
    /**
     * pushingConfigId app任务才能用null
     */
    public pushingConfigId: string | null,
    public thumbUrl: string,
    public brief: string,
    public content: string,
    public createTime: Date,
    public timming?: TimeStamp | null,
  ) {}

  static fromConfig(
    taskId: string,
    platform: Platform,
    accountId: string,
    editContentType: EditContentType,
    pushContentType: PushContentType,
    //app任务才能用null
    pushingConfigId: string | null,
    content: string,
    thumbUrl: string,
    brief: string,
    dateTime: Date,
    timming?: TimeStamp | null,
  ): PushingTask {
    return new PushingTask(
      taskId,
      platform,
      accountId,
      editContentType,
      pushContentType,
      pushingConfigId,
      thumbUrl,
      brief,
      content,
      dateTime,
      timming,
    )
  }
}
