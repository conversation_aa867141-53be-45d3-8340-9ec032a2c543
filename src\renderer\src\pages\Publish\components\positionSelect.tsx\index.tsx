import type { AccountSession, PlatformDataItem } from '@common/structure'
import type { SpiderAccount, Platform } from '@renderer/infrastructure/model'
import { useMemo } from 'react'
import { SelectPositionItem } from './SelectPositionItem'
import { usePlatformRepresentativeAccountContext } from '@renderer/context/platform-representative-account-context'
import { SelectPositionByDouyinItem } from './SelectPositionByDouyinItem'
import { platformNames } from '@common/model/platform-name'

export function PositionSelector({
  value,
  onChange,
  allowedPlatforms,
  selectedAccounts,
}: {
  value: {
    [key: string]: PlatformDataItem<unknown> | null
  }
  selectedAccounts: SpiderAccount[]
  onChange: (value: { [key: string]: PlatformDataItem<unknown> | null }) => void
  allowedPlatforms: Platform[]
}) {
  const handleSelect = (selection: PlatformDataItem | null, platform: string) => {
    if (!platform) return
    onChange({
      ...value,
      [platform]: selection,
    })
  }

  const platformRepresentativeAccount = usePlatformRepresentativeAccountContext()

  const showPlatforms = useMemo(() => {
    const items: {
      name: string
      account: SpiderAccount
      session: AccountSession
      value: PlatformDataItem | null
    }[] = []

    allowedPlatforms.forEach((platform) => {
      const pair = platformRepresentativeAccount[platform.name]
      if (!pair || !pair.session) return

      items.push({
        name: platform.name,
        account: pair.account,
        session: pair.session,
        value: value[platform.name] ?? null,
      })
    })
    return items
  }, [allowedPlatforms, platformRepresentativeAccount, value])

  return (
    <div className="flex flex-wrap gap-5">
      {showPlatforms.map((item) => {
        if (item.account.platform.name === platformNames.DouYin) {
          return (
            <SelectPositionByDouyinItem
              selectedAccounts={selectedAccounts}
              key={item.name}
              account={item.account}
              session={item.session}
              value={
                item.value as PlatformDataItem<{
                  raw: {
                    spu_count?: number
                    cps_spu_count?: number
                    [key: string]: unknown
                  }
                  isBringLocations?: boolean
                  accountId?: string
                }>
              }
              onChange={(value) => handleSelect(value, item.name)}
            />
          )
        }

        return (
          <SelectPositionItem
            key={item.name}
            account={item.account}
            session={item.session}
            value={item.value}
            onChange={(value) => handleSelect(value, item.name)}
          />
        )
      })}
    </div>
  )
}
