import type { MusicPlatformDataItem } from '@common/structure'
import type { ImageFileInfoStructure, VideoFileInfoStructure } from '@common/model'
import type { VideoContentViewModel, VideoPlatformConfigs } from '@renderer/infrastructure/model'
import type { ArticlePlatformConfigs } from '@renderer/infrastructure/model/config/article-platforms'

export interface VideoPushingConfigStoreObject {
  type: 'cloud' | 'local' | 'dynamic' | 'article'
  configId: string
  accountId: string
  platformName: string
  contentTypeName: string
  title: string
  description?: string
  content?: string
  isOriginal?: boolean
  isFirst?: boolean
  locationKeyword: string
  location: VideoContentViewModel['location'] | null
  cover: ImageFileInfoStructure | null
  verticalCover?: ImageFileInfoStructure
  video?: VideoFileInfoStructure
  videoUrl?: string
  coverUrl?: string
  token?: string
  appTaskId?: string
  music?: { [key: string]: MusicPlatformDataItem | null }
  images?: ImageFileInfoStructure[]
  category?: string[]
  timing?: number
  toDraft?: boolean
  platformConfigs?: VideoPlatformConfigs | ArticlePlatformConfigs // 为空是为了兼容老数据
  tags?: string[]
}
