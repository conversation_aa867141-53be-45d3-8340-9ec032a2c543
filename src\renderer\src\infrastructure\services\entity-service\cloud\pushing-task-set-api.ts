import type {
  Operator,
  PushingTask,
  P<PERSON><PERSON><PERSON>Account,
  Pushing<PERSON>ask<PERSON>ushR<PERSON>ult,
  PushingTaskSet,
  PushingTaskSetIdentifier,
  PushingTaskSetViewModel,
} from '@renderer/infrastructure/model'
import { getEditContentTypeByName } from '@renderer/infrastructure/model'
import { getPlatformByName } from '@renderer/infrastructure/model'
import type { PlatformAuditResult } from '@renderer/infrastructure/model/pushing-task/platform-audit-result'
import { apiConverter } from '@renderer/infrastructure/services/network-service/api-converter'

import { useCallback, useMemo } from 'react'
import {
  useBareResponseUserApiService,
  useUserApiService,
} from '@renderer/infrastructure/services/network-service'

import type { PushSetStateFilterOption } from '@renderer/pages/Publish/components/PushStateFilter'
import type {
  AccountForm,
  PagedResponse,
  PushingTaskSetResponse,
  TaskSetCreateRequest,
  TaskSetForm,
} from '@renderer/infrastructure/types'
import {
  type InfiniteQueryResponse,
  PagedResponse2InfiniteQueryResponse,
} from '@renderer/infrastructure/types'
import { AxiosError } from 'axios'
import { eventBus } from '@renderer/infrastructure/event-bus/buses'
import { authenticationEvents } from '@renderer/infrastructure/event-bus/business-events'
import type { ContentTypeFilterOption } from '@renderer/pages/Publish/components/ContentTypeFilter'
import type { CloudPublishTaskVideoFromLite } from '@yixiaoer/platform-service'

export function usePushingTaskSetApi() {
  const userApiService = useUserApiService()
  const bareResponseUserApiService = useBareResponseUserApiService()

  const reportTaskSets = useCallback(
    async (
      {
        taskSet,
        tasks,
      }: {
        taskSet: PushingTaskSet
        tasks: {
          task: PushingTask
          auditResult: PlatformAuditResult | null
          pushResult: PushingTaskPushResult
          account: PushingTaskAccount | undefined
        }[]
      },
      toDraft?: boolean,
    ) => {
      try {
        const response = await bareResponseUserApiService.post(`/taskSets`, {
          /**
           * 任务集ID
           */
          taskSetId: taskSet.taskSetId,
          /**
           * 任务集封面存储Key
           */
          coverKey: taskSet.thumbUrl,
          /**
           * 描述
           */
          desc: taskSet.brief,
          /**
           * 账号媒体ID数组
           */
          platforms: taskSet.platformNames,
          /**
           * 任务集发布类型
           */
          publishType: taskSet.contentType,
          /**
           * 草稿
           */
          isDraft: toDraft,
          /**
           * 是否定时
           */
          isTimed: tasks.find((x) => x.task.timming)?.task.timming,
          /**
           * 子任务数组
           */
          tasks: tasks.map(({ task, auditResult, pushResult, account }) => ({
            /**
             * 封面
             */
            cover: task.thumbUrl,
            /**
             * 描述
             */
            desc: task.brief,
            /**
             * 平台返回的任务ID
             */
            documentId: auditResult?.documentId,
            /**
             * 错误信息
             */
            errorMessage: pushResult.message,
            /**
             * 账号媒体ID
             */
            platformAccountId: account?.accountId,
            /**
             * 平台返回的发布ID
             */
            publishId: pushResult.publishId,
            /**
             * 编辑内容类型
             */
            publishType: task.editContentType,
            /**
             * 推送内容类型
             */
            mediaType: task.pushContentType,
            /**
             * 审核状态
             */
            reviewStatus: undefined,
            /**
             * 任务ID
             */
            taskId: task.taskId,
            /**
             * 视频地址
             */
            videoUrl: task.content,
          })),
        })
        return response
      } catch (error) {
        if (error instanceof AxiosError) {
          if (error.response?.status === 409) {
            return
          } else {
            const status = error?.response?.status
            if (status) {
              if (status >= 500) {
                eventBus.emit(authenticationEvents.serverError, '服务器异常')
              } else if (status >= 400 && status < 500) {
                const message = error?.response?.data?.message
                if (status === 401) {
                  eventBus.emit(authenticationEvents.unauthorized, message || '登录失效')
                } else {
                  eventBus.emit(authenticationEvents.businessError, message || '无权限访问')
                }
              }
            }
          }
        }
        throw error
      }
    },
    [bareResponseUserApiService],
  )

  const publishTaskSet = useCallback(
    (body: TaskSetCreateRequest<TaskSetForm>) => {
      return userApiService.post<string>(`/taskSets`, body)
    },
    [userApiService],
  )

  const getPublishForm = useCallback(
    (taskSetId: string) => {
      return userApiService.get<CloudPublishTaskVideoFromLite>(`/taskSets/${taskSetId}/publishForm`)
    },
    [userApiService],
  )

  const getTaskSets = useCallback(
    async (
      pushState: PushSetStateFilterOption,
      operators: Operator[],
      contentType: ContentTypeFilterOption,
      cursor: Date | null,
    ): Promise<InfiniteQueryResponse<PushingTaskSetViewModel, Date | null>> => {
      const response = await userApiService.get<PagedResponse<PushingTaskSetResponse>>(
        `/taskSets`,
        {
          userIds: operators.map((x) => x.id),
          time: apiConverter.date2APINumber(cursor),
          taskSetStatus: pushState !== '全部' ? pushState : undefined,
          publishType: contentType !== '全部' ? contentType : undefined,
          size: '50',
        },
      )
      return PagedResponse2InfiniteQueryResponse(
        response,
        cursor,
        (x) =>
          ({
            taskSetId: x.id,
            brief: x.desc ?? '',
            thumbUrl: x.coverUrl ?? '',
            state: apiConverter.apiTaskSetStatus2PushingSetState(x.taskSetStatus),
            platforms: x.platforms.map((platformName) => getPlatformByName(platformName)),
            contentType: getEditContentTypeByName(x.publishType),
            createTime: apiConverter.apiNumber2Date(x.createdAt),
            operatorId: x.userId,
            operatorName: x.nickName,
            isFromApp: x.isAppContent,
            isDraft: x.isDraft,
            isCloudTaskSet: x.publishChannel === 'cloud',
          }) satisfies PushingTaskSetViewModel as PushingTaskSetViewModel,
        (response) =>
          response.data.length === 0
            ? cursor
            : apiConverter.apiNumber2Date(
                response.data.reduce((prev, current) =>
                  prev.createdAt < current.createdAt ? prev : current,
                ).createdAt,
              ),
      )
    },
    [userApiService],
  )

  const getTaskSet = useCallback(
    async (taskSetId: PushingTaskSetIdentifier) => {
      const response = await userApiService.get<PushingTaskSetResponse>(`/taskSets/${taskSetId}`)
      return {
        taskSetId,
        brief: response.desc ?? '',
        thumbUrl: response.coverUrl ?? '',
        state: apiConverter.apiTaskSetStatus2PushingSetState(response.taskSetStatus),
        platforms: response.platforms.map((platformName) => getPlatformByName(platformName)),
        contentType: getEditContentTypeByName(response.publishType),
        createTime: apiConverter.apiNumber2Date(response.createdAt),
        operatorId: response.userId,
        operatorName: response.nickName,
        isFromApp: response.isAppContent,
        isDraft: response.isDraft,
        isCloudTaskSet: response.publishChannel === 'cloud',
      } satisfies PushingTaskSetViewModel as PushingTaskSetViewModel
    },
    [userApiService],
  )

  const getFormData = useCallback(
    async (taskSetId: PushingTaskSetIdentifier) => {
      const response = await userApiService.get<{ formData: TaskSetForm }>(
        `/taskSets/${taskSetId}/publishForm`,
      )
      //TODO 这里后续会在formData的同级下的data下保存完整的accounts表单，而不是从formData中获取
      return {
        commonForm: response.formData,
        accountForms: response.formData.accounts.map((item) => {
          const { accountId, ...accountFormData } = item
          return {
            accountId: accountId as string,
            formData: accountFormData as AccountForm,
          } satisfies {
            accountId: string
            formData: AccountForm
          }
        }),
      }
    },
    [userApiService],
  )

  return useMemo(
    () => ({
      reportTaskSets,
      getTaskSets,
      getTaskSet,
      publishTaskSet,
      getPublishForm,
      getFormData,
    }),
    [reportTaskSets, getTaskSets, getTaskSet, publishTaskSet, getPublishForm, getFormData],
  )
}
