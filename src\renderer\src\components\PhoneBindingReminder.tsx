import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { Button } from '@renderer/shadcn-components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@renderer/shadcn-components/ui/dialog'
import { useFeatureManager } from '@renderer/infrastructure/services'
import { features } from '@renderer/infrastructure/model'
import { UserInfoMenu, useUserInfoMenuStore } from '@renderer/store/userInfoMenuStore'

interface PhoneBindingReminderProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onNavigateToSettings: () => void
}

export const PhoneBindingReminder = ({
  open,
  onOpenChange,
  onNavigateToSettings,
}: PhoneBindingReminderProps) => {
  const { openFeature } = useFeatureManager()
  const { setDefaultMenu } = useUserInfoMenuStore()

  const handleBindNow = () => {
    onOpenChange(false)
    onNavigateToSettings()
    // 设置默认菜单为账号安全
    setDefaultMenu(UserInfoMenu.Password)
    // 打开个人设置页面
    openFeature(features.个人设置)
  }

  const handleSkip = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>手机绑定提醒</DialogTitle>
          <VisuallyHidden>
            <DialogDescription>
              基于网络安全和管理的相关法律法规规定，互联网产品落实网络实名制，请绑定手机号。
            </DialogDescription>
          </VisuallyHidden>
        </DialogHeader>

        <div className="space-y-4">
          <div className="text-sm leading-relaxed text-muted-foreground">
            基于网络安全和管理的相关法律法规规定，互联网产品落实网络实名制，请绑定手机号。
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={handleSkip}>
              暂不绑定
            </Button>
            <Button onClick={handleBindNow}>立即绑定</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
