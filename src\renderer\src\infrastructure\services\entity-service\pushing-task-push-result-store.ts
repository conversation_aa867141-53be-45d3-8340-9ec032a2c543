import { PushingTaskPushResult } from '../../model/pushing-task'
import type { PushingTaskPushResultRepository } from '../../repository/repositories'
import { usePushingTaskPushResultRepository } from '../../repository/repositories'
import type { PushingTaskPushResultStoreObject } from '../../entity'
import { useMemo } from 'react'

export function usePushingTaskPushResultStore() {
  const pushingTaskStateRepository = usePushingTaskPushResultRepository()
  return useMemo(
    () => new PushingTaskPushResultStore(pushingTaskStateRepository),
    [pushingTaskStateRepository],
  )
}

export class PushingTaskPushResultStore {
  constructor(private repository: PushingTaskPushResultRepository) {}

  private _Model2Entity(pushingTaskPushResult: PushingTaskPushResult) {
    return {
      taskId: pushingTaskPushResult.taskId,
      state: pushingTaskPushResult.state,
      message: pushingTaskPushResult.message,
      publishId: pushingTaskPushResult.publishId,
    } satisfies PushingTaskPushResultStoreObject as PushingTaskPushResultStoreObject
  }

  private _Object2Model(entity: PushingTaskPushResultStoreObject): PushingTaskPushResult {
    return new PushingTaskPushResult(entity.taskId, entity.state, entity.message, entity.publishId)
  }

  async save(pushingTaskPushResult: PushingTaskPushResult) {
    await this.repository.save(this._Model2Entity(pushingTaskPushResult))
  }

  async get(taskId: string) {
    return this._Object2Model(await this.repository.get(taskId))
  }

  async getAll(taskIds: string[]) {
    const storeObjects = await this.repository.getAll(taskIds)
    return storeObjects.map(this._Object2Model)
  }

  async getAllUnfinishedStates() {
    const storeObjects = await this.repository.getMany((x) =>
      x.where('state').noneOf(['推送成功', '推送失败']).toArray(),
    )
    return storeObjects.map(this._Object2Model)
  }

  async delete(taskIds: string[]) {
    await this.repository.bulkDelete(taskIds)
  }

  getAllSuccessStates() {
    return this.repository.getMany((x) => x.where('state').anyOf(['推送成功']).toArray())
  }
}
