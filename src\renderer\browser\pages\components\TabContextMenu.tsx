import { browserChannel } from '@common/events/browser-events'
import type { BrowserTabListItem } from '@common/structure/browser/header'
import type { ReactNode } from 'react'
import { useEffect, useRef, useState } from 'react'
import { useBrowserService } from '@browser/infrastructure/services/browser-service'
import { Overlay } from '@browser/pages/components/overlay'

export function TabContextMenu({
  children,
  tab,
}: {
  children: ReactNode
  tab: BrowserTabListItem
}) {
  const browserService = useBrowserService()
  const [open, setOpen] = useState(false)

  const [position, setPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 })
  const contextMenuRef = useRef<HTMLDivElement | null>(null)

  const handleContextMenu = (event: React.MouseEvent) => {
    event.preventDefault()
    setPosition({ x: event.clientX, y: event.clientY })
    setOpen(true)
  }

  useEffect(() => {
    if (open && contextMenuRef.current) {
      const { x, y } = position
      contextMenuRef.current.style.top = `${y}px`
      contextMenuRef.current.style.left = `${x}px`
    }
  }, [open, position])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (contextMenuRef.current && !contextMenuRef.current.contains(event.target as Node)) {
        browserService.setTabViewTop()
        setOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [browserService, setOpen])

  useEffect(() => {
    return window.api.on(browserChannel.tabViewTopped, () => {
      setOpen(false)
    })
  }, [])

  useEffect(() => {
    if (open) {
      browserService.setHeaderViewTop()
    }
  }, [browserService, open])

  return (
    <>
      <Overlay
        open={open}
        onClick={() => {
          console.log(1)
          browserService.setTabViewTop()
          setOpen(false)
        }}
      />
      <div onContextMenu={handleContextMenu} className="inline-block max-w-[134px] flex-grow">
        {children}
        {open && (
          <>
            <div
              onContextMenu={(event) => event.stopPropagation()}
              ref={contextMenuRef}
              className="absolute z-50 rounded-xl border border-gray-300 bg-white shadow-lg"
            >
              <div
                className="m-1 cursor-pointer rounded-xl p-2 hover:bg-gray-100"
                onClick={() => {
                  browserService.closeOtherTab(tab.id)
                  browserService.setTabViewTop()
                  setOpen(false)
                }}
              >
                关闭其他标签页
              </div>
              <div
                className="m-1 cursor-pointer rounded-xl p-2 hover:bg-gray-100"
                onClick={() => {
                  browserService.closeRightTab(tab.id)
                  browserService.setTabViewTop()
                  setOpen(false)
                }}
              >
                关闭右侧标签页
              </div>
            </div>
          </>
        )}
      </div>
    </>
  )
}
