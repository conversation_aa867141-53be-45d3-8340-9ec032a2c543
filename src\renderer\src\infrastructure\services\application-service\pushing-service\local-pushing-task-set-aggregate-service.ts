import type {
  PushingTaskSetOperatorStore,
  PushingTaskSetStore,
} from '@renderer/infrastructure/services/entity-service'
import { usePushingTaskSetStore } from '@renderer/infrastructure/services/entity-service'
import { usePushingTaskSetOperatorStore } from '@renderer/infrastructure/services/entity-service/pushing-task-set-operator-store'
import { useMemo } from 'react'
import type { PushingTaskSet } from '@renderer/infrastructure/model'
import type { PushingTaskSetAggregate } from '@renderer/infrastructure/model/pushing-task/pushing-task-set-view-model'
import type { PushingTaskSetOperator } from '@renderer/infrastructure/model/pushing-task/pushing-task-set-operator'

/**
 * 本地推送任务聚合服务，这个服务用于对外提供完整的推送任务聚合，只依赖数据库
 */
export function useLocalPushingTaskSetAggregateService() {
  const pushingTaskSetStore = usePushingTaskSetStore()
  const pushingTaskSetOperatorStore = usePushingTaskSetOperatorStore()
  return useMemo(
    () => new LocalPushingTaskSetAggregateService(pushingTaskSetStore, pushingTaskSetOperatorStore),
    [pushingTaskSetOperatorStore, pushingTaskSetStore],
  )
}

export class LocalPushingTaskSetAggregateService {
  constructor(
    private pushingTaskSetStore: PushingTaskSetStore,
    private pushingTaskSetOperatorStore: PushingTaskSetOperatorStore,
  ) {}

  async getAllTaskSetAggregate(): Promise<PushingTaskSetAggregate[]> {
    const taskSets = await this.pushingTaskSetStore.getEntireSets()

    const taskSetIds = taskSets.map((x) => x.taskSetId)

    const operators = await this.pushingTaskSetOperatorStore.getMany(taskSetIds)

    return this.toPushingTaskGroup(taskSets, operators)
  }

  private toPushingTaskGroup(taskSets: PushingTaskSet[], operators: PushingTaskSetOperator[]) {
    return taskSets.map((taskSet) => {
      const operator = operators.find((operator) => operator.taskSetId === taskSet.taskSetId)
      return {
        taskSet,
        operator: operator!,
      } satisfies PushingTaskSetAggregate as PushingTaskSetAggregate
    })
  }
}
