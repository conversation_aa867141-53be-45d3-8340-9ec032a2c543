import { Repository } from '@renderer/infrastructure/repository'
import type { PushingTaskPushResultStoreObject } from '@renderer/infrastructure/entity'
import type { MemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemo } from 'react'

export function usePushingTaskPushResultRepository() {
  const database = useMemberDatabase()
  return useMemo(() => new PushingTaskPushResultRepository(database), [database])
}

export class PushingTaskPushResultRepository extends Repository<
  PushingTaskPushResultStoreObject,
  'taskId'
> {
  constructor(database: MemberDatabase) {
    super(database.instance.PushingTaskPushResult)
  }
}
