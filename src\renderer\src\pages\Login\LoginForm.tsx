import { Input } from '@renderer/shadcn-components/ui/input'
import { Button } from '@renderer/shadcn-components/ui/button'
import PhoneIcon from '@renderer/assets/passport/phone.svg?react'
import VerifyIcon from '@renderer/assets/passport/verify.svg?react'
import PasswordIcon from '@renderer/assets/passport/password.svg?react'
import { Checkbox } from '@renderer/shadcn-components/ui/checkbox'
import { FormItem as LoginFormItem } from '@renderer/pages/Login/FormItem'
// import { Head2, Paragraph, Terms } from '@renderer/pages/Login/Terms'
import { useEffect, useState } from 'react'
import { useNotify } from '@renderer/hooks/use-notify'
import { useNavigate } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { RuleResult } from '@renderer/infrastructure/validation/rule'
import { Validator } from '@renderer/infrastructure/validation/validator'
import { useLoginService, useGlobalStorageService } from '@renderer/infrastructure/services'
import { useValidation } from '@renderer/hooks/validation/validation'
import { animated, useSpring } from '@react-spring/web'
import { Tabs, TabsList, TabsTrigger } from '@renderer/shadcn-components/ui/tabs'
import { PasswordInput } from '@renderer/components/PasswordInput'
import { alertBaseManager } from '@renderer/components/alertBase'

const phoneNumberValidator = Validator.of<string>().addRule((subject) => {
  const phoneNumberRegex = /^(1[3-9][0-9])\d{8}$/
  if (!phoneNumberRegex.test(subject)) {
    return new RuleResult('invalid', '手机号码格式不正确')
  }
  return RuleResult.valid
})

const verifyCodeValidator = Validator.of<string>().addRule((subject) => {
  const verifyCodeRegex = /^\d{6}$/
  if (!verifyCodeRegex.test(subject)) {
    return new RuleResult('invalid', '验证码格式不正确')
  }
  return RuleResult.valid
})

const dummyValidator = Validator.of<string>().addRule(() => RuleResult.valid)

const passwordValidator = Validator.of<string>().addRule((subject) => {
  if (!subject) {
    return new RuleResult('invalid', '请输入密码')
  }
  return RuleResult.valid
})

export function RenderLoginForm() {
  const springs = useSpring({
    from: { opacity: 0 },
    to: { opacity: 1 },
    config: { duration: 200 },
    delay: 300,
  })

  const loginService = useLoginService()
  const globalStorageService = useGlobalStorageService()
  const navigate = useNavigate()
  const { notifyService } = useNotify()

  const [phoneNumber, setPhoneNumber] = useState('')
  const [verifyCode, setVerifyCode] = useState('')
  const [password, setPassword] = useState('')

  const [accepted, setAccepted] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [rememberPassword, setRememberPassword] = useState(false)

  const [tab, setTab] = useState<'code' | 'password'>('password')

  const isCode = tab === 'code'

  const { mutate: loginMutate, isPending } = useMutation({
    mutationFn: () =>
      loginService.login(
        tab === 'code'
          ? { username: phoneNumber, code: verifyCode }
          : { username: phoneNumber, password },
      ),
    onSuccess: () => {
      // 保存最后使用的手机号
      globalStorageService.setLastUsedPhone(phoneNumber)

      // 处理记住密码逻辑
      if (tab === 'password') {
        if (rememberPassword) {
          globalStorageService.setRememberedPassword(phoneNumber, password)
        } else {
          globalStorageService.clearRememberedPassword(phoneNumber)
        }
      }
      navigate('/newMain')
    },
  })

  const isWaiting = countdown > 0

  const { conclusion: phoneNumberConclusion } = useValidation(phoneNumber, phoneNumberValidator)

  const { conclusion: verifyCodeConclusion } = useValidation(
    verifyCode,
    isCode ? verifyCodeValidator : dummyValidator,
  )
  const { conclusion: passwordConclusion } = useValidation(
    password,
    isCode ? dummyValidator : passwordValidator,
  )

  const canSendVerifyCode = phoneNumberConclusion.valid && !isWaiting

  const canLogin = verifyCodeConclusion.valid && passwordConclusion.valid && accepted && !isPending

  // 初始化时自动填充最后使用的手机号和协议同意状态，并设置默认tab
  useEffect(() => {
    const lastUsedPhone = globalStorageService.getLastUsedPhone()
    if (lastUsedPhone) {
      setPhoneNumber(lastUsedPhone)

      // 检查是否有记住的密码来决定默认tab
      const rememberedPassword = globalStorageService.getRememberedPassword(lastUsedPhone)
      if (rememberedPassword) {
        // 有记住的密码，保持密码登录tab
        setTab('password')
      } else {
        // 没有记住的密码，切换到验证码登录tab
        setTab('code')
      }
    } else {
      // 没有最后使用的手机号，切换到验证码登录tab
      setTab('code')
    }

    // 自动加载协议同意状态
    const termsAccepted = globalStorageService.getTermsAccepted()
    if (termsAccepted) {
      setAccepted(true)
    }
  }, [globalStorageService])

  // 当手机号码变化时，检查是否有记住的密码
  useEffect(() => {
    if (tab === 'password') {
      if (phoneNumber) {
        const rememberedPassword = globalStorageService.getRememberedPassword(phoneNumber)
        if (rememberedPassword) {
          setPassword(rememberedPassword)
          setRememberPassword(true)
        } else {
          setPassword('')
          setRememberPassword(false)
        }
      } else {
        // 当手机号为空时，清空密码和记住密码状态
        setPassword('')
        setRememberPassword(false)
      }
    }
  }, [phoneNumber, tab, globalStorageService])

  // 当协议同意状态变化时，自动保存
  useEffect(() => {
    globalStorageService.setTermsAccepted(accepted)
  }, [accepted, globalStorageService])

  useEffect(() => {
    if (countdown === 0) {
      return
    }

    const timeout = setTimeout(() => {
      setCountdown((countdown) => countdown - 1)
    }, 1000)

    return () => clearInterval(timeout)
  }, [countdown])

  async function sendVerifyCode() {
    if (!canSendVerifyCode) {
      return
    }

    // 防止重复运行
    if (isWaiting) {
      return
    }

    const non_production_code = await loginService.sendVerifyCode(phoneNumber)

    if (non_production_code) {
      notifyService.info(`测试验证码：${non_production_code}`)
      setVerifyCode(non_production_code)
    }

    setCountdown(() => 60)
  }
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === 'Enter' && canLogin) {
        loginMutate()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [canLogin, loginMutate])
  return (
    <animated.div style={springs}>
      <div className="mt-8 flex w-full flex-col items-center gap-4">
        <Tabs value={tab} onValueChange={(value) => setTab(value as 'code' | 'password')}>
          <TabsList>
            <TabsTrigger value="password">密码登录</TabsTrigger>
            <TabsTrigger value="code">验证码登录</TabsTrigger>
          </TabsList>
        </Tabs>
        <div className="grid w-[358px] grid-cols-1 gap-4">
          <LoginFormItem prepend={<PhoneIcon />}>
            <Input
              type="text"
              placeholder={tab === 'code' ? '手机号码' : '手机账号/账号'}
              className="h-[54px] bg-background/50 pl-10"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
            />
          </LoginFormItem>
          {tab === 'password' ? (
            <LoginFormItem prepend={<PasswordIcon />}>
              <PasswordInput
                asChild
                placeholder="密码"
                className="h-[54px] bg-background/50 pl-10"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </LoginFormItem>
          ) : (
            <LoginFormItem
              prepend={<VerifyIcon />}
              append={
                <Button variant="link" onClick={sendVerifyCode} disabled={!canSendVerifyCode}>
                  {isWaiting ? `重新发送(${countdown})秒` : '获取验证码'}
                </Button>
              }
            >
              <Input
                type="text"
                placeholder="验证码"
                className="h-[54px] bg-background/50 pl-10 pr-28"
                value={verifyCode}
                onChange={(e) => setVerifyCode(e.target.value)}
              />
            </LoginFormItem>
          )}
          <div className="flex h-5 items-center justify-between">
            {tab === 'password' ? (
              <>
                <LoginFormItem className="gap-1">
                  <Checkbox
                    id="remember"
                    checked={rememberPassword}
                    onClick={() => setRememberPassword(!rememberPassword)}
                  />
                  <label
                    htmlFor="remember"
                    className="cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    记住密码
                  </label>
                </LoginFormItem>
                <Button
                  variant="link"
                  className="h-5 p-0 text-primary hover:no-underline"
                  onClick={() => {
                    alertBaseManager.open({
                      title: '忘记密码',
                      description: '请先用验证码登录后，在个人设置中重置密码。',
                      buttons: [],
                      okText: '我知道了',
                    })
                  }}
                >
                  忘记密码
                </Button>
              </>
            ) : (
              // 验证码登录模式下的占位空间，保持与密码登录相同的高度
              <div className="w-full"></div>
            )}
          </div>
        </div>

        <div className="flex-0 mt-10 h-max w-full">
          <Button className="h-12 w-full" disabled={!canLogin} onClick={() => loginMutate()}>
            登录
          </Button>
        </div>

        <div className="mt-0 flex h-0 w-full items-center justify-start">
          <LoginFormItem className="gap-1">
            <Checkbox id="terms" checked={accepted} onClick={() => setAccepted(!accepted)} />
            <label
              htmlFor="terms"
              className="cursor-pointer text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              我已阅读并接受
              <span className="text-primary">
                《
                <Button
                  variant="link"
                  onClick={() => {
                    window.open(
                      'https://lite-download.yixiaoer.cn/privacy/yixiaoeruseragreement.pdf',
                    )
                  }}
                  className="p-0"
                >
                  用户服务协议
                </Button>
                》
              </span>
              和
              <span className="text-primary">
                《
                <Button
                  variant="link"
                  onClick={() => {
                    window.open(
                      'https://lite-download.yixiaoer.cn/privacy/yixiaoerprivacypolicy.pdf',
                    )
                  }}
                  className="p-0"
                >
                  隐私政策
                </Button>
                》
              </span>
            </label>
          </LoginFormItem>
        </div>
      </div>
    </animated.div>
  )
}
