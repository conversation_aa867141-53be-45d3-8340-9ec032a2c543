import type { Account } from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'

import type { AccountSession, WithLocalStorage } from '@common/structure'
import type { LocalArticlePushingConfig } from '@renderer/infrastructure/model/config/pushing-config'
import type { IDayuhaoArticleData } from '@yixiaoer/platform-service'

class DaYuHaoPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toArticle(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalArticlePushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      save: toDraft,
      title: config.title,
      covers: [
        {
          width: config.cover!.width,
          height: config.cover!.height,
          size: config.cover!.size,
          pathOrUrl: config.cover!.path,
        },
      ],
      content: config.content,
      //@ts-ignore 等待类型修复为可空
      cancelToken: undefined,
      prePubTime: config.timing ?? 0,
    } satisfies IDayuhaoArticleData & WithLocalStorage
  }
}

export const daYuHao = new DaYuHaoPushingTaskBodyConverter()
