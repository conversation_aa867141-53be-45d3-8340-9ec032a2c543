import { type MemberDatabase, useMemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemo } from 'react'
import { Repository } from '@renderer/infrastructure/repository'
import type { PublishStoreObject } from '@renderer/infrastructure/entity'

export function usePublishRepository() {
  const database = useMemberDatabase()
  return useMemo(() => new PublishRepository(database), [database])
}

export class PublishRepository extends Repository<PublishStoreObject, 'taskId'> {
  constructor(database: MemberDatabase) {
    super(database.instance.Publish)
  }
}
