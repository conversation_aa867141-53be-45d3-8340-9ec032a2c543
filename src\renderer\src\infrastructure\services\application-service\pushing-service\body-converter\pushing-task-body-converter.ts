import type { Account, ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'

import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { AccountSession } from '@common/structure'
import type {
  LocalArticlePushingConfig,
  LocalImageTextPushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { PushContentType } from '@common/model/content-type'

export interface PushingTaskBodyConverter {
  toVideo?(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject>

  toDynamic?(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalImageTextPushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject>
  toArticle?(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalArticlePushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject>
}

export async function ignoreException(func: () => PromiseLike<void> | void): Promise<void> {
  try {
    await func()
  } catch (e) {
    console.error(e)
  }
}
