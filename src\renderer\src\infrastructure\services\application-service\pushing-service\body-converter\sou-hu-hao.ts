import type { Account, ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'

import type { AccountSession, WithLocalStorage } from '@common/structure'
import type {
  LocalArticlePushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { SouhuhaoArticleTaskInput, SouhuhaoVideoTaskInput } from '@yixiaoer/platform-service'
import { platformNames } from '@common/model/platform-name'

class SouHuHaoPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    const platformConfig = pushingConfig.platformConfigs[platformNames.SouHuHao]
    const categories = platformConfig.categories.map((x) => x.raw)

    if (!categories) {
      throw new Error('搜狐号分类不能为空')
    }

    return {
      localStorage: session.localStorage,
      taskId: taskId,
      video: {
        duration: pushingConfig.video!.fileDurationTimeSpan.seconds,
        width: pushingConfig.video!.videoWidth,
        height: pushingConfig.video!.videoHeight,
        size: pushingConfig.video!.fileSize,
        localPath: pushingConfig.video!.filePath,
      },
      cover: {
        width: pushingConfig.cover!.width,
        height: pushingConfig.cover!.height,
        size: pushingConfig.cover!.size,
        pathOrUrl: pushingConfig.cover!.path,
      },
      desc: pushingConfig.description,
      title: pushingConfig.title,
      pubType: toDraft ? 0 : 1,
      /**
       * 一级分类id
       */
      category: categories[0],
      /**
       * 二级分类id
       */
      subCategory: categories[1],
      /**
       * 话题
       */
      topics: [],
      /**
       * 专栏id
       */
      columnIds: undefined,
      /**
       * 信息来源 0无特别声明，1引用申明，2自行拍摄，3包含AI创作内容，4包含虚构创作
       */
      infoResource: 0,
      /**
       * 来源链接
       */
      sourceUrl: undefined,
    } satisfies SouhuhaoVideoTaskInput & WithLocalStorage
  }

  async toArticle(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalArticlePushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      pubType: toDraft ? 0 : 1,
      title: config.title,
      covers: [
        {
          width: config.cover!.width,
          height: config.cover!.height,
          size: config.cover!.size,
          pathOrUrl: config.cover!.path,
        },
      ],
      desc: '',
      content: config.content,
      //@ts-ignore 等待类型修复为可空
      cancelToken: undefined,
      publishTime: config.timing!,
    } satisfies SouhuhaoArticleTaskInput & WithLocalStorage
  }
}

export const souHuHao = new SouHuHaoPushingTaskBodyConverter()
