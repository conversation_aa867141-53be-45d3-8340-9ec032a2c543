import type { Account, ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'

import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import type { XiguaVideoTaskInput } from '@yixiaoer/platform-service/dist/media-platform/xiguashipin'
import type { AccountSession, WithLocalStorage } from '@common/structure'
import { datetimeService } from '@renderer/infrastructure/services'
import type { LocalVideoPushingConfig } from '@renderer/infrastructure/model/config/pushing-config'

class XiGuaShiPinPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      pubType: toDraft ? 0 : 1,
      video: {
        duration: video.fileDurationTimeSpan.seconds,
        width: video.videoWidth,
        height: video.videoHeight,
        size: video.fileSize,
        localPath: video.filePath,
      },
      cover: {
        width: cover.width,
        height: cover.height,
        size: cover.size,
        pathOrUrl: cover.path,
      },
      title: title,
      topics: [],
      mentions: [],
      desc: undefined,
      visibility_type: 0,
      download: 0,
      timing: datetimeService.toSeconds(timing),
      should_sync: 0,
      activity: undefined,
      isOriginal: isOriginal ? 1 : 0,
    } satisfies XiguaVideoTaskInput & WithLocalStorage
  }
}

export const xiGuaShiPin = new XiGuaShiPinPushingTaskBodyConverter()
