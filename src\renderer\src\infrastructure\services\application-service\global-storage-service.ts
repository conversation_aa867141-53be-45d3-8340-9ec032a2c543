import { localStorageService } from '@renderer/infrastructure/services/storage-service'
import { useMemo } from 'react'
import { useContextStore } from '@renderer/store/contextStore'
import type { UserInfo } from '@renderer/infrastructure/model'
import { phoneRegex } from '@renderer/utils/zod'

export function useGlobalStorageService() {
  const { userInfo } = useContextStore((state) => ({
    userInfo: state.userInfo,
  }))
  return useMemo(() => new GlobalStorageService(userInfo), [userInfo])
}

export class GlobalStorageService {
  constructor(private userInfo: UserInfo | null) {}

  setToken(token: string) {
    localStorageService.setItem('token', token)
  }

  getToken(): string | null {
    return localStorageService.getItem<string>('token')
  }

  clearToken() {
    localStorageService.removeItem('token')
  }

  getLastMessageDatetime(): Date | null {
    const result = localStorageService.getItem<number>(`${this.userInfo?.id}_lastMessageDatetime`)
    return result ? new Date(result) : null
  }

  setLastMessageDatetime(date: Date | null): void {
    localStorageService.setItem(
      `${this.userInfo?.id}_lastMessageDatetime`,
      date ? date.getTime() : null,
    )
  }

  setRememberedPassword(phone: string, password: string): void {
    localStorageService.setItem(`remembered_password_${phone}`, password)
  }

  getRememberedPassword(phone: string): string | null {
    return localStorageService.getItem<string>(`remembered_password_${phone}`)
  }

  clearRememberedPassword(phone: string): void {
    localStorageService.removeItem(`remembered_password_${phone}`)
  }

  hasRememberedPassword(phone: string): boolean {
    return this.getRememberedPassword(phone) !== null
  }

  // 最后使用的手机号管理
  private readonly LAST_PHONE_KEY = 'last_used_phone'

  // 最后使用的用户名
  private readonly LAST_USERNAME_KEY = 'last_used_username'

  /**
   * 验证手机号格式
   */
  private isValidPhoneNumber(phone: string): boolean {
    if (!phone || typeof phone !== 'string') return false
    return phoneRegex.test(phone.trim())
  }

  /**
   * 保存最后使用的用户名
   */
  setLastUsedUsername(username: string): void {
    try {
      const trimmedUsername = username.trim()
      localStorageService.setItem(this.LAST_USERNAME_KEY, trimmedUsername)
    } catch (error) {
      console.error('Failed to save last used username:', error)
    }
  }

  /**
   * 获取最后使用的用户名
   */
  getLastUsedUsername(): string | null {
    try {
      const username = localStorageService.getItem<string>(this.LAST_USERNAME_KEY)

      return username
    } catch (error) {
      console.warn('Failed to get last used phone:', error)
      return null
    }
  }

  /**
   * 保存最后使用的手机号
   */
  setLastUsedPhone(phone: string): void {
    try {
      if (!this.isValidPhoneNumber(phone)) {
        console.warn('Invalid phone number format, not saving:', phone)
        return
      }

      const trimmedPhone = phone.trim()
      localStorageService.setItem(this.LAST_PHONE_KEY, trimmedPhone)
    } catch (error) {
      console.error('Failed to save last used phone:', error)
    }
  }

  /**
   * 获取最后使用的手机号
   */
  getLastUsedPhone(): string | null {
    try {
      const phone = localStorageService.getItem<string>(this.LAST_PHONE_KEY)

      // 验证获取到的手机号格式是否仍然有效
      if (phone && this.isValidPhoneNumber(phone)) {
        return phone
      }

      // 如果格式无效，清除存储的数据
      if (phone) {
        this.clearLastUsedPhone()
      }

      return null
    } catch (error) {
      console.warn('Failed to get last used phone:', error)
      return null
    }
  }

  /**
   * 清除最后使用的手机号
   */
  clearLastUsedPhone(): void {
    try {
      localStorageService.removeItem(this.LAST_PHONE_KEY)
    } catch (error) {
      console.error('Failed to clear last used phone:', error)
    }
  }

  // 用户协议同意状态管理
  private readonly TERMS_ACCEPTED_KEY = 'terms_accepted'

  /**
   * 保存用户协议同意状态
   */
  setTermsAccepted(accepted: boolean): void {
    try {
      localStorageService.setItem(this.TERMS_ACCEPTED_KEY, accepted)
    } catch (error) {
      console.error('Failed to save terms accepted status:', error)
    }
  }

  /**
   * 获取用户协议同意状态
   */
  getTermsAccepted(): boolean {
    try {
      const accepted = localStorageService.getItem<boolean>(this.TERMS_ACCEPTED_KEY)
      return accepted === true
    } catch (error) {
      console.warn('Failed to get terms accepted status:', error)
      return false
    }
  }

  /**
   * 清除用户协议同意状态
   */
  clearTermsAccepted(): void {
    try {
      localStorageService.removeItem(this.TERMS_ACCEPTED_KEY)
    } catch (error) {
      console.error('Failed to clear terms accepted status:', error)
    }
  }
}
