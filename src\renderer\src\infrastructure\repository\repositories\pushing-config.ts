import { Repository } from '../index'
import type { VideoPushingConfigStoreObject } from '../../entity'
import type { MemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemo } from 'react'

export function useVideoPushingConfigRepository() {
  const database = useMemberDatabase()
  return useMemo(() => new VideoPushingConfigRepository(database), [database])
}

export class VideoPushingConfigRepository extends Repository<
  VideoPushingConfigStoreObject,
  'configId'
> {
  constructor(database: MemberDatabase) {
    super(database.instance.PushingConfig)
  }
}
