import type { LocalAuditStatus } from '@common/structure'
import type { AuditState } from '@renderer/infrastructure/types'

export enum PlatformResultStage {
  UPLOAD = 'upload',
  PUSH = 'push',
  TRANSCODING = 'transcoding',
  REVIEW = 'review',
  SCHEDULED = 'scheduled',
  SUCCESS = 'success',
}

export enum PlatformResultStageStatus {
  DOING = 'doing',
  SUCCESS = 'success',
  FAIL = 'fail',
}

export interface PlatformResult {
  documentId: string | null
  openUrl: string | null
  message: string
  publishId: string | null
  stageStatus: PlatformResultStageStatus
  stages: PlatformResultStage
}

export function toAuditResultState(state: LocalAuditStatus | '查询失败'): AuditState {
  switch (state) {
    case '转码中':
      return '转码中'
    case '转码失败':
      return '转码失败'
    case '审核中':
    case '查询失败': // 这里是历史数据兼容处理，审核状态（结果）中不应该存在查询失败，这是查询状态，2025年2月19日
      return '审核中'
    case '已发布':
    case '草稿':
      return '审核成功'
    case '定时发布':
      return '平台定时发布中'
    case '未通过':
    case '用户撤回，下线，删除':
    case '不适合公开':
    case '非公开的':
      return '审核失败'
    default:
      throw new Error(`不支持的审核结果转换:${state}`)
  }
}

export class PlatformAuditResult {
  constructor(
    public taskId: string,
    public state: LocalAuditStatus = '审核中',
    public documentId: string | null = null,
    public openUrl: string | null = null,
    public message: string = '',
  ) {}

  get auditState(): AuditState {
    return toAuditResultState(this.state)
  }
}
