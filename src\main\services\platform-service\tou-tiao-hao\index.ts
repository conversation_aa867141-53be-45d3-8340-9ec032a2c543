import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type {
  ToutiaohaoArticleTaskInput,
  ToutiaohaoMiniVideoTaskInput,
  ToutiaohaoVideoTaskInput,
} from '@yixiaoer/platform-service/dist/media-platform/toutiaohao'
import type { PlatformDataItem } from '@common/structure'
import { Platforms } from '@yixiaoer/platform-service'

class TouTiaoHaoPlatformService extends PlatformService {
  constructor() {
    super(platformNames.TouTiaoHao)
  }
  async pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)

    const result = await this.getPushingResult(async (eventEmitter) => {
      if (contentTypeName === 'verticalVideo') {
        return (await getPlatformServicePromise()).Toutiaohao.publishMiniVideo(
          cookie,
          body as ToutiaohaoMiniVideoTaskInput,
          eventEmitter,
        )
      } else {
        return (await getPlatformServicePromise()).Toutiaohao.publishVideo(
          cookie,
          body as ToutiaohaoVideoTaskInput,
          eventEmitter,
        )
      }
    }, progressCallBack)

    return result.publishId!
  }

  async pushArticle(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
  ): Promise<string> {
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(async (eventEmitter) => {
      return (await getPlatformServicePromise()).Toutiaohao.publishArticle(
        cookie,
        body as ToutiaohaoArticleTaskInput,
        eventEmitter,
      )
    }, progressCallBack)

    return result.publishId!
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    return this.convert2PlatformAccountInfo(
      async () => (await getPlatformServicePromise()).Toutiaohao.getToutiaohaoUserInfo(cookie),
      (x) =>
        new AuthorizingAccountInfo(
          platformNames.TouTiaoHao,
          x.data!.user.id.toString(),
          x.data!.user.screen_name ?? x.data!.media.display_name,
          x.data!.user.https_avatar_url ?? x.data!.media.https_avatar_url,
          x.verify,
        ),
    )
  }

  async getTopics(cookies: Electron.Cookie[], keyword: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Toutiaohao.getToutiaohaoVideoTopiclist(
          this.convertCookie(cookies),
          keyword,
        ),
      (x) => x.data ?? [],
    )

    return result.map((item) => ({
      id: item,
      text: item,
      raw: item,
    }))
  }

  async queryAccountOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    const data = await platformService.DataService.getAccountReport(Platforms.TouTiaoHao, cookie)

    return {
      video: data.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    const videoOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.TouTiaoHao,
          false,
          cookie,
          'video',
        ),
      (x) => x.data || [],
    )
    const miniVideoOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.TouTiaoHao,
          false,
          cookie,
          'miniVideo',
        ),
      (x) => x.data || [],
    )
    const articleOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.TouTiaoHao,
          false,
          cookie,
          'article',
        ),
      (x) => x.data || [],
    )
    return [...videoOverviews, ...miniVideoOverviews, ...articleOverviews]
  }
}

export const touTiaoHaoPlatformService = new TouTiaoHaoPlatformService()
