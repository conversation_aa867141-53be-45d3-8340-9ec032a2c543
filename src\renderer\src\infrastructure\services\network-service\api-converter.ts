import type { AccountState, PushingTaskSetState, PushState } from '@renderer/infrastructure/model'
import { unknowFeatureGroup } from '@renderer/infrastructure/model'
import { featureGroups } from '@renderer/infrastructure/model'
import { TaskSetStatusResponse } from '@renderer/infrastructure/types'
import type { SessionState } from '@common/structure'

class ApiDataConverter {
  accountState2API(state: AccountState) {
    switch (state) {
      case '正常':
        return 1
      case '已失效':
        return 2
      case '未实名':
        return 3
      default:
        throw new Error(`不支持的API数据转换:登录状态:${state}`)
    }
  }

  sessionState2API(state: SessionState) {
    switch (state) {
      case '正常':
        return 1
      case '已失效':
        return 2
      default:
        throw new Error(`不支持的API数据转换:登录状态:${state}`)
    }
  }

  sessionState2Model(state: 0 | 1 | 2 | 3 | 4): SessionState {
    switch (state) {
      case 1:
        return '正常'
      case 2:
        return '已失效'
      default:
        return '已失效'
    }
  }

  pushState2API(state: PushState) {
    switch (state) {
      case '视频上传中':
        return 0
      case '等待推送':
      case '正在推送':
      case '推送成功':
        return 1
      case '推送失败':
        return -1
      default:
        throw new Error(`不支持的API数据转换:推送状态:${state}`)
    }
  }

  apiNumber2Date(date: number) {
    return new Date(date)
  }

  apiString2Date(date: string) {
    return new Date(date)
  }

  date2APINumber(cursor: Date | null): string | undefined {
    return cursor ? cursor.getTime().toString() : undefined
  }

  apiTaskSetStatus2PushingSetState(taskSetStatus: TaskSetStatusResponse): PushingTaskSetState {
    switch (taskSetStatus) {
      case TaskSetStatusResponse.Publishing:
        return '发布中'
      case TaskSetStatusResponse.Allsuccessful:
        return '全部发布成功'
      case TaskSetStatusResponse.Allfailed:
        return '全部发布失败'
      case TaskSetStatusResponse.Partialsuccessful:
        return '部分发布成功'
      default:
        throw new Error(`不支持的API数据转换:任务集状态:${taskSetStatus}`)
    }
  }

  api2ShortcutFeatureGroup(featureGroupName: string) {
    return featureGroups[featureGroupName] ?? unknowFeatureGroup
  }
}

export const apiConverter = new ApiDataConverter()
