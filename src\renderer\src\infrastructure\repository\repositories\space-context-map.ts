import { useMemo } from 'react'
import { Repository } from '../index'
import type { SpaceContextMapStoreObject } from '../../entity'
import type { MemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemberDatabase } from '@renderer/infrastructure/repository/storage'

export class SpaceContextMapRepository extends Repository<SpaceContextMapStoreObject, 'spaceId'> {
  constructor(database: MemberDatabase) {
    super(database.instance.SpaceContextMap)
  }
}

export function useSpaceContextMapRepository() {
  const database = useMemberDatabase()

  return useMemo(() => new SpaceContextMapRepository(database), [database])
}
