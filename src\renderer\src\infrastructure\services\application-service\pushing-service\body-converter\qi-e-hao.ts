import type { Account } from '@renderer/infrastructure/model'
import { type ImageFileInfo, type VideoFileInfo } from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import { datetimeService } from '@renderer/infrastructure/services'

import type { AccountSession, WithLocalStorage } from '@common/structure'
import type {
  LocalArticlePushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { QiehaoArticleTaskInput, QiehaoVideoTaskInput } from '@yixiaoer/platform-service'
import { platformNames } from '@common/model/platform-name'

class QiEHaoPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    const platformConfig = pushingConfig.platformConfigs[platformNames.QiEHao]
    const categories = platformConfig.categories.map((x) => x.raw)

    if (!categories) {
      throw new Error('企鹅号分类不能为空')
    }

    const tags = pushingConfig.tags

    if (!tags) {
      throw new Error('企鹅号标签不能为空')
    }

    return {
      localStorage: session.localStorage,
      taskId: taskId,
      video: {
        duration: pushingConfig.video!.fileDurationTimeSpan.seconds,
        width: pushingConfig.video!.videoWidth,
        height: pushingConfig.video!.videoHeight,
        size: pushingConfig.video!.fileSize,
        localPath: pushingConfig.video!.filePath,
      },
      cover: {
        width: pushingConfig.cover!.width,
        height: pushingConfig.cover!.height,
        size: pushingConfig.cover!.size,
        pathOrUrl: pushingConfig.cover!.path,
      },
      desc: description,
      title: title,
      tags: tags,
      category: categories[0].categoryName,
      subCategory: categories[1].categoryName,
      pubType: toDraft ? 0 : 1,
      publishTime: datetimeService.toSeconds(datetimeService.roundToMinutes(timing!, 5)),
    } satisfies QiehaoVideoTaskInput & WithLocalStorage
  }

  async toArticle(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalArticlePushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      pubType: toDraft ? 0 : 1,
      title: config.title,
      covers: [
        {
          width: config.cover!.width,
          height: config.cover!.height,
          size: config.cover!.size,
          pathOrUrl: config.cover!.path,
        },
      ],
      content: config.content,
      //@ts-ignore 等待类型修复为可空
      cancelToken: undefined,
      publishTime: datetimeService.toSeconds(datetimeService.roundToMinutes(config.timing!, 5)),
      tags: [],
    } satisfies QiehaoArticleTaskInput & WithLocalStorage
  }
}

export const qiEHao = new QiEHaoPushingTaskBodyConverter()
