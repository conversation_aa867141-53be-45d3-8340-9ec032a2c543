import type { Account, ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import { utils } from '@coozf/editor'
import { ignoreException } from './pushing-task-body-converter'
import { bilibiliPlatformService, datetimeService } from '@renderer/infrastructure/services'
import type { BilibiliVideoTaskInput } from '@yixiaoer/platform-service/dist/media-platform/bilibili/bilibili-video.service'
import type { AccountSession, WithLocalStorage } from '@common/structure'
import type { LocalVideoPushingConfig } from '@renderer/infrastructure/model/config/pushing-config'
import { platformNames } from '@common/model/platform-name'
import type { PushContentType } from '@common/model/content-type'

class BilibiliPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    const platformConfig = pushingConfig.platformConfigs[platformNames.BiliBili]
    // 获取分类（分区）
    const categories = platformConfig?.categories

    let categoryId: number

    if (!categories || categories.length === 0) {
      const result = await bilibiliPlatformService.getVideoCategory(session)
      categoryId = result[0]?.children ? Number(result[0].children[0].id) : 0
    } else {
      categoryId = categories[categories.length - 1].raw.id
    }

    const tags = pushingConfig.tags

    // 这段逻辑仅为了兼容APP发布，APP发布目前不支持tags 2025年1月6日
    if (tags.length === 0) {
      // 转换tags
      description = await utils.commonTopic.modifyTopics(description, async (element) => {
        await ignoreException(async () => {
          const keyword = element.getAttribute('text')

          if (!keyword) return

          tags.push(keyword)

          element.remove()
        })
      })
    }

    return {
      localStorage: session.localStorage,
      pubType: toDraft ? 0 : 1,
      taskId: taskId,
      video: {
        duration: video.fileDurationTimeSpan.seconds,
        width: video.videoWidth,
        height: video.videoHeight,
        size: video.fileSize,
        localPath: video.filePath,
      },
      cover: {
        width: cover.width,
        height: cover.height,
        size: cover.size,
        pathOrUrl: cover.path,
      },
      title: title,
      desc: description,
      copyright: isOriginal ? 1 : 2,
      source: '',
      no_reprint: 1,
      open_elec: 0,
      tags: tags,
      tid: categoryId,
      up_close_reply: false,
      up_close_danmu: false,
      up_selection_reply: false,
      dtime: datetimeService.toSeconds(timing),
    } satisfies BilibiliVideoTaskInput & WithLocalStorage
  }
}

export const bilibili = new BilibiliPushingTaskBodyConverter()
