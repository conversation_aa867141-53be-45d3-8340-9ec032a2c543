import type { PushingTaskAccountRepository } from '../../repository/repositories'
import { usePushingTaskAccountRepository } from '../../repository/repositories'
import type { PushingTaskAccountStoreObject } from '../../entity'
import { PushingTaskAccount } from '@renderer/infrastructure/model'
import { useMemo } from 'react'

function _Object2Model(object: PushingTaskAccountStoreObject): PushingTaskAccount {
  return new PushingTaskAccount(
    object.taskId,
    object.accountId,
    object.accountName,
    object.accountRemark,
    object.avatarUrl,
  )
}

export function usePushingTaskAccountStore() {
  const pushingTaskAccountRepository = usePushingTaskAccountRepository()
  return useMemo(
    () => new PushingTaskAccountStore(pushingTaskAccountRepository),
    [pushingTaskAccountRepository],
  )
}

export class PushingTaskAccountStore {
  constructor(private pushingTaskAccountRepository: PushingTaskAccountRepository) {}

  async save(
    taskId: string,
    accountId: string,
    accountName: string,
    accountRemark: string,
    avatarUrl: string,
  ) {
    await this.pushingTaskAccountRepository.save({
      taskId,
      accountId,
      accountName,
      accountRemark,
      avatarUrl,
    } satisfies PushingTaskAccountStoreObject as PushingTaskAccountStoreObject)
  }

  async get(taskId: string) {
    return await this.pushingTaskAccountRepository.get(taskId)
  }

  // del
  async delete(taskId: string | string[]) {
    if (Array.isArray(taskId)) {
      return await this.pushingTaskAccountRepository.bulkDelete(taskId)
    } else {
      return await this.pushingTaskAccountRepository.delete(taskId)
    }
  }

  async getMany(taskIds: string[]) {
    return (
      await this.pushingTaskAccountRepository.getMany((x) =>
        x.where('taskId').anyOf(taskIds).toArray(),
      )
    ).map(_Object2Model)
  }
}
