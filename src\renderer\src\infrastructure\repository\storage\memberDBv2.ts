import type { EntityTable } from 'dexie'
import { <PERSON>ie } from 'dexie'
import type {
  PlatformAuditResultStoreObject,
  PushingTaskPushResultStoreObject,
  PushingTaskSetOperatorStoreObject,
  PushingTaskReportingStateStoreObject,
  PushingTaskStoreObject,
  SchedulingPlatformStateQueryStoreObject,
  VideoPushingConfigStoreObject,
  PushingTaskAccountStoreObject,
  SpaceContextMapStoreObject,
  OverviewStoreObject,
  PushingTaskSetStoreObject,
  PublishStoreObject,
} from '../../entity'
import { identifierService } from '@common/infrastructure/services/identifier-service'
import { toAuditResultState, getPushingTaskSetState } from '@renderer/infrastructure/model'
import { getTaskState } from '@renderer/infrastructure/model/pushing-task/task-state'

export type MemberTableDeclaration = {
  Publish: EntityTable<PublishStoreObject, 'taskId', PublishStoreObject>
  PushingTaskSet: EntityTable<PushingTaskSetStoreObject, 'taskSetId', PushingTaskSetStoreObject>
  PushingTask: EntityTable<PushingTaskStoreObject, 'taskId', PushingTaskStoreObject>
  PushingTaskSetOperator: EntityTable<
    PushingTaskSetOperatorStoreObject,
    'taskSetId',
    PushingTaskSetOperatorStoreObject
  >
  PushingTaskAccount: EntityTable<
    PushingTaskAccountStoreObject,
    'taskId',
    PushingTaskAccountStoreObject
  >
  PushingTaskSetReportingState: EntityTable<
    PushingTaskReportingStateStoreObject,
    'taskSetId',
    PushingTaskReportingStateStoreObject
  >
  PushingTaskPushResult: EntityTable<
    PushingTaskPushResultStoreObject,
    'taskId',
    PushingTaskPushResultStoreObject
  >
  PlatformAuditResult: EntityTable<
    PlatformAuditResultStoreObject,
    'taskId',
    PlatformAuditResultStoreObject
  >
  SchedulingPlatformStateQuery: EntityTable<
    SchedulingPlatformStateQueryStoreObject,
    'taskId',
    SchedulingPlatformStateQueryStoreObject
  >
  PushingConfig: EntityTable<
    VideoPushingConfigStoreObject,
    'configId',
    VideoPushingConfigStoreObject
  >
  SpaceContextMap: EntityTable<SpaceContextMapStoreObject, 'spaceId', SpaceContextMapStoreObject>

  Overview: EntityTable<OverviewStoreObject, 'accountId', OverviewStoreObject>
}

function createMemberDB(identifier: string) {
  const db = new Dexie(identifier) as Dexie & MemberTableDeclaration
  db.version(2)
    .stores({
      Publish: '&taskId, taskSetId',
      PushingTaskSet: '&taskSetId, createTime',
      PushingTask: '&taskId, createTime, accountId',
      PushingTaskSetOperator: '&taskSetId, operatorId',
      PushingTaskAccount: '&taskId, accountId',
      PushingTaskSetReportingState: '&taskSetId, reported',
      PushingTaskPushResult: '&taskId, state',
      PlatformAuditResult: '&taskId, state',
      SchedulingPlatformStateQuery: '&taskId',
      PushingConfig: '&configId',
      SpaceContextMap: '&spaceId, contextId',
      Overview: '&accountId',
    })
    .upgrade(async (tx) => {
      const tasks = await tx.table('PushingTask').toArray()
      const operators = await tx.table('PushingTaskOperator').toArray()
      const pushResults = await tx.table('PushingTaskPushResult').toArray()
      const auditResults = await tx.table('PlatformAuditResult').toArray()

      const groupedTasks = tasks.reduce(
        (groups, task) => {
          const groupKey = Math.floor(new Date(task.createTime).getTime() / 5000)
          if (!groups[groupKey]) {
            groups[groupKey] = []
          }
          groups[groupKey].push(task)
          return groups
        },
        {} as Record<number, PushingTaskStoreObject[]>,
      )

      for (const groupKey in groupedTasks) {
        const taskSetId = identifierService.generateUUID()
        const group = groupedTasks[groupKey]

        const first_task = group[0]

        // 计算任务集的状态
        const groupTaskStates = pushResults
          .filter((x) => group.map((y) => y.taskId).includes(x.taskId))
          .map((x) => {
            const pushState = x.state
            const auditState = auditResults.find((audit) => audit.taskId === x.taskId)?.state
            const taskState = getTaskState(
              pushState,
              auditState !== undefined ? toAuditResultState(auditState) : null,
            )
            console.log(
              x.taskId,
              '任务状态',
              taskState,
              pushState,
              auditState !== undefined ? toAuditResultState(auditState) : null,
            )
            return taskState
          })

        pushResults.filter((x) => group.map((x) => x.taskId).includes(x.taskId)).map((x) => x.state)

        const state = getPushingTaskSetState(groupTaskStates)
        console.log('任务集状态', state)

        // 添加任务集
        await tx.table('PushingTaskSet').add({
          taskSetId,
          brief: first_task.brief,
          contentTypeName: first_task.contentTypeName,
          createTime: first_task.createTime,
          platformNames: Array.from(new Set(group.map((x) => x.platformName))),
          state: state,
          hasFailedTask: groupTaskStates.includes('失败'),
          thumbUrl: first_task.thumbUrl,
        })

        // 添加任务集的上报状态
        await tx.table('PushingTaskSetReportingState').add({
          taskSetId,
          reported: true,
        })

        // 添加任务集的操作员
        const foundOperator = operators.find((x) => x.taskId === first_task.taskId)
        await tx.table('PushingTaskSetOperator').add({
          taskSetId,
          operatorId: foundOperator?.operatorId ?? '',
          operatorName: foundOperator?.operatorName ?? '',
        })

        // 添加任务集的任务到发布关系中
        for (const task of group) {
          await tx.table('Publish').add({
            taskId: task.taskId,
            taskSetId,
          })
        }
      }
    })
  return db
}

export { createMemberDB }
