import { Repository } from '../index'
import type { SchedulingPlatformStateQueryStoreObject } from '../../entity'
import type { MemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemo } from 'react'

export function useSchedulingPlatformStateQueryRepository() {
  const database = useMemberDatabase()
  return useMemo(() => new SchedulingPlatformStateQueryRepository(database), [database])
}

export class SchedulingPlatformStateQueryRepository extends Repository<
  SchedulingPlatformStateQueryStoreObject,
  'taskId'
> {
  constructor(database: MemberDatabase) {
    super(database.instance.SchedulingPlatformStateQuery)
  }
}
