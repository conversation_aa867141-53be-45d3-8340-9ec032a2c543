export interface TaskSetCreateRequest<T extends TaskSetForm> {
  /**
   * 任务集封面存储Key
   */
  coverKey?: string
  /**
   * 描述
   */
  desc: string
  /**
   * 是否为草稿
   */
  isDraft: boolean
  /**
   * 是否定时任务
   */
  isTimed?: number
  /**
   * 账号媒体数组信息
   */
  platformAccounts: PublishAccountRequest[]
  /**
   * 账号媒体ID数组
   */
  platforms: string[]
  /**
   * 发布参数(动态结构, 服务端不做任何处理, 会原封不动的下发给客户端用于发布)
   */
  publishArgs: T
  /**
   * 发布渠道本机发布:local,云端:cloud
   */
  publishChannel: 'local' | 'cloud'
  /**
   * 任务集发布类型
   */
  publishType: string
}

// 服务端账号项
export interface PublishAccountRequest {
  /**
   * 超级编导视频id
   */
  superId?: string
  /**
   * 超级编导锁id
   */
  superLockId?: string
  /**
   * 封面地址(上传到OSS)
   */
  coverKey?: string
  /**
   * 媒体账号id
   */
  platformAccountId: string
  /**
   * 视频地址(上传到天翼云)
   */
  videoKey?: string
}

/**
 * 任务集表单
 */
export type TaskSetForm = VideoTaskSetForm | ArticleTaskSetForm | ImageTextTaskSetForm

/**
 * 账号表单
 */
export type AccountForm = { video?: VideoFormItem; cover?: ImageFormItem }

/**
 * 视频内容表单
 */
export interface VideoTaskSetForm {
  /**
   * 账号及账号与媒体的关联信息
   * @description 每个账号都包含一个视频和一个封面
   */
  accounts: {
    video: VideoFormItem
    cover: ImageFormItem
    accountId: string
  }[]
  /**
   * A类平台表单
   */
  aPlatform: {
    /**
     * 标题
     */
    title: string
    /**
     * 描述
     * @description 有限的html格式支持，有一些特定的标签如<topic />等会被转换为话题
     */
    description: string
  }
  /**
   * B类平台表单
   */
  bPlatform: {
    /**
     * 标题
     */
    title: string
    /**
     * 描述
     */
    description: string
    /**
     * 标签
     */
    tags: string[]
  }
  /**
   * 是否原创
   */
  isOriginal?: boolean
  /**
   * 定时发布时间，Unix时间戳
   */
  timing?: number
  /**
   * 分类
   * @description 分类数据项的键为平台名称，值为级联数据项数组
   */
  categories?: {
    [key: string]: CascadingDataItem[] | undefined
  }
  /**
   * 话题
   * @description 话题数据项的键为平台名称，值为数据项数组
   */
  location?: {
    [key: string]: DataItem | undefined
  }
  /**
   * 是否发布到草稿箱
   * @description 不同平台草稿的形式有所差异
   */
  isDraft: boolean
}

/**
 * 图文内容表单
 */
export interface ImageTextTaskSetForm {
  /**
   * 账号信息
   */
  accounts: {
    accountId: string
  }[]
  /**
   * 标题
   */
  title: string
  /**
   * 描述
   * @description 有限的html格式支持，有一些特定的标签如<topic />等会被转换为话题
   */
  description: string
  /**
   * 位置
   * @description 位置数据项的键为平台名称，值为数据项
   */
  location: {
    [key: string]: DataItem | undefined
  }
  /**
   * 图片集
   */
  images: ImageFormItem[]
  /**
   * 封面
   */
  cover: ImageFormItem | undefined
  /**
   * 音乐
   * @description 音乐数据项的键为平台名称，值为音乐数据项
   */
  music: {
    [key: string]: MusicPlatformDataItem | undefined
  }
  /**
   * 定时发布时间，Unix时间戳
   */
  timing?: number
  /**
   * 是否发布到草稿箱，不同平台草稿的形式有所差异
   */
  isDraft: boolean
}

/**
 * 文章内容表单
 */
export interface ArticleTaskSetForm {
  /**
   * 账号信息
   */
  accounts: {
    accountId: string
  }[]
  /**
   * 封面
   * @description 文章封面图片
   */
  cover?: ImageFormItem
  /**
   * 竖版封面
   * @description 文章竖版封面图片，百家号可选
   */
  verticalCover?: ImageFormItem
  /**
   * 头条首发
   * @description 文章是否为头条首发，头条号可选
   */
  isFirst: boolean
  /**
   * 分类
   * @description 分类数据项的键为平台名称，值为级联数据项数组
   */
  categories?: {
    [key: string]: CascadingDataItem[] | undefined
  }
  /**
   * 位置搜索关键字
   * @description 用于搜索位置数据项，通常是城市名称或地标名称
   */
  locationKeyword?: string
  /**
   * 标题
   */
  title: string
  /**
   * 描述
   * @description 有限的html格式支持
   */
  content: string
  /**
   * 定时发布时间，Unix时间戳
   */
  timing?: number
  /**
   * 话题
   * @description 话题数据项的键为平台名称，值为数据项数组
   */
  topic?: {
    [key: string]: DataItem[] | undefined
  }
  isDraft: boolean
}

/**
 * 图片表单项
 * @description 发布表单中的图片大都遵循此标准
 */
export interface ImageFormItem {
  /**
   * 图片宽度 px
   */
  width: number
  /**
   * 图片高度 px
   */
  height: number
  /**
   * 图片大小 Byte
   */
  size: number
  /**
   * 图片的对象存储key，云发布必填
   */
  key?: string
  /**
   * 图片的本地路径，本地发布必填
   */
  path?: string
}

/**
 * 视频表单项
 * @description 发布表单中的视频大都遵循此标准
 */
export interface VideoFormItem {
  /**
   * 视频时长，单位秒
   */
  duration: number
  /**
   * 视频宽度，单位px
   */
  width: number
  /**
   * 视频高度，单位px
   */
  height: number
  /**
   * 视频大小，单位Byte
   */
  size: number
  /**
   * 视频的对象存储key，云发布必填
   */
  key?: string
  /**
   * 视频的本地路径，本地发布必填
   */
  path?: string
}

/**
 * 一般数据项
 */
interface DataItem<TRaw = unknown> {
  /**
   * 数据项的唯一标识符
   */
  id: string
  /**
   * 数据项的显示文本
   */
  text: string
  /**
   * 数据项的原始数据
   */
  raw: TRaw
}

/**
 * 级联数据项
 * @description 用于表示级联选择中的数据项，通常包含子级数据项
 */
interface CascadingDataItem<TRaw = unknown> extends DataItem<TRaw> {
  /**
   * 子级数据项
   * @description 用于表示级联选择中的子选项
   */
  children?: CascadingDataItem<TRaw>[]
}

/**
 * 音乐数据项
 * @description 用于表示音乐中的音乐数据项
 */
interface MusicPlatformDataItem<TRaw = unknown> {
  /**
   * 音乐数据项的唯一标识符
   */
  id: string
  /**
   * 歌曲名称
   */
  title: string
  /**
   * 歌手名称
   */
  artist: string
  /**
   * 歌曲时长，单位秒
   */
  duration: number
  /**
   * 音乐的封面图片地址
   */
  url: string
  /**
   * 音乐的原始数据
   */
  raw: TRaw
}
