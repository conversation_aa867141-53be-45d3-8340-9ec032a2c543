import { Repository } from '../index'
import type { PushingTaskAccountStoreObject } from '../../entity'
import type { MemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemberDatabase } from '@renderer/infrastructure/repository/storage'
import { useMemo } from 'react'

export function usePushingTaskAccountRepository() {
  const database = useMemberDatabase()
  return useMemo(() => new PushingTaskAccountRepository(database), [database])
}

export class PushingTaskAccountRepository extends Repository<
  PushingTaskAccountStoreObject,
  'taskId'
> {
  constructor(database: MemberDatabase) {
    super(database.instance.PushingTaskAccount)
  }
}
