import { ApiService, BareApiService, WechatTokenApiService } from './api-service'
import { useGeneralResponseInterceptor } from '@renderer/infrastructure/services/network-service/interceptors/general-response-interceptor'
import { useTokenRequestInterceptor } from '@renderer/infrastructure/services/network-service/interceptors/token-request-interceptor'
import { versionRequestInterceptor } from '@renderer/infrastructure/services/network-service/interceptors/version-request-interceptor'
import { useMemo } from 'react'

export function useUserApiService() {
  const tokenRequestInterceptor = useTokenRequestInterceptor()
  const generalResponseInterceptor = useGeneralResponseInterceptor()
  return useMemo(() => {
    const apiService = new ApiService()
    apiService.useInterceptors(
      [versionRequestInterceptor, tokenRequestInterceptor],
      [generalResponseInterceptor],
    )
    return apiService
  }, [generalResponseInterceptor, tokenRequestInterceptor])
}

export function useWechatTokenApiService() {
  const tokenRequestInterceptor = useTokenRequestInterceptor()
  const generalResponseInterceptor = useGeneralResponseInterceptor()
  return useMemo(() => {
    const apiService = new WechatTokenApiService()
    apiService.useInterceptors(
      [versionRequestInterceptor, tokenRequestInterceptor],
      [generalResponseInterceptor],
    )
    return apiService
  }, [generalResponseInterceptor, tokenRequestInterceptor])
}

// 不处理Response，这样可以处理类似409的问题
export function useBareResponseUserApiService() {
  const tokenRequestInterceptor = useTokenRequestInterceptor()
  return useMemo(() => {
    const apiService = new BareApiService()
    apiService.useInterceptors([versionRequestInterceptor, tokenRequestInterceptor], [])
    return apiService
  }, [tokenRequestInterceptor])
}
