import { useEffect, useRef } from 'react'
import { usePushingService } from '@renderer/infrastructure/services'

export function useResume() {
  const { resumeUnfinishedPushingTasks } = usePushingService()
  const hasRunOnceForUnfinishedTasks = useRef(false)

  useEffect(() => {
    if (hasRunOnceForUnfinishedTasks.current) return
    hasRunOnceForUnfinishedTasks.current = true
    void resumeUnfinishedPushingTasks()
  })
}
