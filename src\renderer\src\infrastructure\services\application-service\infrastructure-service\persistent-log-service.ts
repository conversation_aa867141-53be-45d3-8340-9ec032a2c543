import { logChannel } from '@common/events/log-channel'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { AccountSession } from '@common/structure'
import type { Account, PushingTask } from '@renderer/infrastructure/model'
import { useCallback, useMemo } from 'react'
import { useUploadFileService } from '../upload-file-service'
import { useLogsApi } from '../../entity-service/logs-api'

export function usePersistentLogService() {
  const uploadService = useUploadFileService()
  const { uploadLogs } = useLogsApi()

  const logAuthorizeSuccess = useCallback((account: Account, accountSession: AccountSession) => {
    return window.api.send(
      logChannel.authorizeSuccess,
      account.platform.name,
      account.authorId,
      account.nickName,
      accountSession,
    )
  }, [])
  const logAuthorizeUpdate = useCallback((account: Account, accountSession: AccountSession) => {
    return window.api.send(
      logChannel.authorizeUpdate,
      account.platform.name,
      account.authorId,
      account.nickName,
      accountSession,
    )
  }, [])
  const logAuthorizeFail = useCallback(
    (account: Account, detail: string | unknown, session: AccountSession) => {
      return window.api.send(
        logChannel.authorizeFail,
        account.platform.name,
        account.authorId,
        account.nickName,
        detail,
        session,
      )
    },
    [],
  )
  const logPublishScheduling = useCallback(
    (
      task: PushingTask,
      account: Account,
      wechatToken: [string, string] | null,
      session: AccountSession,
      body: LiteralObject,
    ) => {
      return window.api.send(
        logChannel.publishScheduling,
        task.taskId,
        task.platform.name,
        account.authorId,
        account.nickName,
        wechatToken,
        session,
        body,
      )
    },
    [],
  )

  const uploadLogArchive = useCallback(
    async (logId: string) => {
      const archive = await window.api.invoke<Uint8Array>(logChannel.getLogArchive)
      const { key, serviceUrl } = await uploadService.getUploadUrl('logs')
      await uploadService.putUint8Array(serviceUrl, archive)
      uploadLogs(logId, key)
    },
    [uploadLogs, uploadService],
  )

  return useMemo(
    () => ({
      logAuthorizeSuccess,
      logAuthorizeFail,
      logPublishScheduling,
      logAuthorizeUpdate,
      uploadLogArchive,
    }),
    [
      logAuthorizeSuccess,
      logAuthorizeFail,
      logPublishScheduling,
      logAuthorizeUpdate,
      uploadLogArchive,
    ],
  )
}
