import type {
  Account,
  ImageFileInfo,
  VideoContentViewModel,
  VideoFileInfo,
} from '@renderer/infrastructure/model'
import { platforms } from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'

import type { PushingTaskBodyConverter } from './pushing-task-body-converter'
import { ignoreException } from './pushing-task-body-converter'
import { utils } from '@coozf/editor'
import type {
  AccountSession,
  MusicPlatformDataItem,
  PlatformDataItem,
  WithLocalStorage,
} from '@common/structure'
import type { ShipinhaoDynamicTaskInput, ShipinhaoLocationInfo } from '@yixiaoer/platform-service'
import { datetimeService, weiXinShiPinHaoPlatformService } from '@renderer/infrastructure/services'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { ShipinhaoVideoTaskInput } from '@yixiaoer/platform-service/dist/media-platform/shipinhao/shipinhao-video.service'
import type {
  LocalImageTextPushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { PlatformMusicInfo } from '@yixiaoer/platform-service/dist/media-platform/model'

class WeiXinShiPinHaoPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    // 转换话题
    description = await this.getDescription(description, account, session)
    // 获取地理位置
    const location: PlatformDataItem<ShipinhaoLocationInfo> | undefined = await this.getLocation(
      locationKeyword,
      pushingConfig.location,
      account,
      session,
    )

    return {
      localStorage: session.localStorage,
      pubType: toDraft ? 0 : 1,
      taskId: taskId,
      video: {
        duration: video.fileDurationTimeSpan.seconds,
        width: video.videoWidth,
        height: video.videoHeight,
        size: video.fileSize,
        localPath: video.filePath,
      },
      cover: {
        width: cover.width,
        height: cover.height,
        size: cover.size,
        pathOrUrl: cover.path,
      },
      desc: description,
      shortTitle: '',
      uin: Number.parseInt(account.authorId),
      finderUsername: '',
      mentionUser: [],
      postFlag: isOriginal ? 1 : 0,
      link: undefined,
      linkTitle: undefined,
      goods: undefined,
      collection: undefined,
      effectiveTime: datetimeService.toSeconds(timing),
      event: undefined,
      topics: [],
      location: location?.raw,
    } satisfies ShipinhaoVideoTaskInput & WithLocalStorage
  }

  async toDynamic(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalImageTextPushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    // 转换话题
    const description = await this.getDescription(config.description, account, session)
    // 获取地理位置
    const location = await this.getLocation(
      config.locationKeyword,
      config.location,
      account,
      session,
    )
    const music = config.music?.[
      platforms.WeiXinShiPinHao.name
    ] as MusicPlatformDataItem<PlatformMusicInfo>
    return {
      localStorage: session.localStorage,
      pubType: toDraft ? 0 : 1,
      taskId: taskId,
      title: config.title,
      images: config.images.map((image) => ({
        width: image.width,
        height: image.height,
        size: image.size,
        pathOrUrl: image.path,
      })),
      desc: description,
      uin: Number.parseInt(account.authorId),
      finderUsername: '',
      mentionUser: [],
      goods: undefined,
      collection: undefined,
      event: undefined,
      topics: [],
      location: location?.raw,
      music: music?.raw,
      effectiveTime: datetimeService.toSeconds(config.timing),
    } satisfies ShipinhaoDynamicTaskInput & WithLocalStorage
  }

  private async getLocation(
    locationKeyword: string,
    configLocation: VideoContentViewModel['location'] | null,
    account: Account,
    session: AccountSession,
  ) {
    if (configLocation && configLocation[platforms.WeiXinShiPinHao.name]) {
      return configLocation[
        platforms.WeiXinShiPinHao.name
      ] as PlatformDataItem<ShipinhaoLocationInfo>
    }
    let location: PlatformDataItem<ShipinhaoLocationInfo> | undefined
    await ignoreException(async () => {
      location = locationKeyword
        ? (
            await weiXinShiPinHaoPlatformService.getLocations(account, session, locationKeyword)
          )[0] ?? undefined
        : undefined
    })
    return location
  }

  private async getDescription(description: string, account: Account, session: AccountSession) {
    return await utils.commonTopic.modifyTopics(description, async (element) => {
      await ignoreException(async () => {
        const keyword = element.getAttribute('text')
        if (!keyword) return

        const topic =
          (await weiXinShiPinHaoPlatformService.getTopics(account, session, keyword))[0] ??
          undefined
        if (!topic) element.remove()
        else element.setAttribute('raw', JSON.stringify(topic.raw))
      })
    })
  }
}

export const weiXinShiPinHao = new WeiXinShiPinHaoPushingTaskBodyConverter()
