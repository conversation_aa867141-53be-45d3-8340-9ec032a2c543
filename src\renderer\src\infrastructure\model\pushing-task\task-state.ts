import type { PushState } from '@renderer/infrastructure/model'
import type { AuditState } from '@renderer/infrastructure/types'

export type TaskState = '发布中' | '成功' | '失败' | AuditState | PushState

export function getTaskState(pushState: PushState, auditState: AuditState | null): TaskState {
  if (auditState !== null) {
    switch (auditState) {
      case '审核中':
        return '发布中'
      case '审核失败':
        return '失败'
      case '审核成功':
      case '平台定时发布中':
        return '成功'
      default:
        return auditState
    }
  }

  switch (pushState) {
    case '视频上传中':
    case '等待推送':
    case '正在推送':
    case '推送成功':
      return '发布中'
    case '推送失败':
      return '失败'
    default:
      return pushState
  }
}
