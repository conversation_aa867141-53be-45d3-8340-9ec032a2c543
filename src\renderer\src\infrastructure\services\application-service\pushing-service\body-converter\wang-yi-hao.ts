import type { Account, ImageFileInfo, VideoFileInfo } from '@renderer/infrastructure/model'
import type { PushContentType } from '@common/model/content-type'
import type { LiteralObject } from '@common/infrastructure/type/literal-object'
import type { PushingTaskBodyConverter } from './pushing-task-body-converter'

import type { AccountSession, WithLocalStorage } from '@common/structure'
import type {
  LocalArticlePushingConfig,
  LocalVideoPushingConfig,
} from '@renderer/infrastructure/model/config/pushing-config'
import type { WangyihaoArticleTaskInput, WangyiVideoTaskInput } from '@yixiaoer/platform-service'
import { platformNames } from '@common/model/platform-name'
import { datetimeService } from '@renderer/infrastructure/services'

class WangYiHaoPushingTaskBodyConverter implements PushingTaskBodyConverter {
  async toVideo(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    pushingConfig: LocalVideoPushingConfig,
    video: VideoFileInfo,
    cover: ImageFileInfo,
    title: string,
    description: string,
    isOriginal: boolean,
    locationKeyword: string,
    toDraft: boolean,
    timing?: number,
  ): Promise<LiteralObject> {
    const platformConfig = pushingConfig.platformConfigs[platformNames.WangYiHao]
    const categories = platformConfig.categories.map((x) => x.raw)

    if (!categories) {
      throw new Error('网易号分类不能为空')
    }

    const tags = pushingConfig.tags

    if (!tags) {
      throw new Error('网易号标签不能为空')
    }

    return {
      localStorage: session.localStorage,
      taskId: taskId,
      video: {
        duration: pushingConfig.video!.fileDurationTimeSpan.seconds,
        width: pushingConfig.video!.videoWidth,
        height: pushingConfig.video!.videoHeight,
        size: pushingConfig.video!.fileSize,
        localPath: pushingConfig.video!.filePath,
      },
      cover: {
        width: pushingConfig.cover!.width,
        height: pushingConfig.cover!.height,
        size: pushingConfig.cover!.size,
        pathOrUrl: pushingConfig.cover!.path,
      },
      title: title,
      /**
       * 话题列表，可多个
       */
      topics: undefined,
      /**
       * 标签
       */
      tags: tags,
      /**
       *创作申明
       */
      creativeStatement: undefined,
      /**
       * 一级分类
       */
      category: categories[0],
      /**
       * 二级分类
       */
      subCategory: categories[1],
      /**
       * 定时发布日期 格式:2024-03-12 14:13:01
       */
      publishTime: timing ? datetimeService.format(timing, 'yyyy-MM-dd HH:mm:ss') : undefined,
      isOriginal: pushingConfig.isOriginal ? 1 : 0,
      pubType: toDraft ? 0 : 1,
    } satisfies WangyiVideoTaskInput & WithLocalStorage
  }

  async toArticle(
    taskId: string,
    account: Account,
    session: AccountSession,
    contentType: PushContentType,
    config: LocalArticlePushingConfig,
    toDraft: boolean,
  ): Promise<LiteralObject> {
    return {
      localStorage: session.localStorage,
      taskId: taskId,
      pubType: toDraft ? 0 : 1,
      title: config.title,
      covers: [
        {
          width: config.cover!.width,
          height: config.cover!.height,
          size: config.cover!.size,
          pathOrUrl: config.cover!.path,
        },
      ],
      content: config.content,
      //@ts-ignore 等待类型修复为可空
      cancelToken: undefined,
      publishTime: config.timing ?? undefined,
    } satisfies WangyihaoArticleTaskInput & WithLocalStorage
  }
}

export const wangYiHao = new WangYiHaoPushingTaskBodyConverter()
