import { immerable } from 'immer'
import type { TimeStamp } from '@renderer/infrastructure/types/brand'
import type { ImageFileInfo, SpiderAccount, VideoFileInfo } from '@renderer/infrastructure/model'
import { platformNames } from '@common/model/platform-name'
import type { CascadingPlatformDataItem, PlatformDataItem } from '@common/structure'
import type {
  AiQiYiVideoCategoryItem,
  BilibiliVideoCategoryInfo,
  QiEHaoVideoCategoryItem,
  YidianhaoCategory,
  ZhiHuVideoCategoryItem,
} from '@renderer/infrastructure/services'
import { randomService } from '@renderer/infrastructure/services'
import type { WangyiVideoCategory } from '@yixiaoer/platform-service/dist/media-platform/wangyihao/wangyihao-category.service'
import type { SouhuhaoVideoCategoryItem } from '@yixiaoer/platform-service'

export class VideoCategories {
  [immerable] = true;

  [index: string]: CascadingPlatformDataItem[]

  [platformNames.ZhiHu]: CascadingPlatformDataItem<ZhiHuVideoCategoryItem>[] = [];
  [platformNames.QiEHao]: CascadingPlatformDataItem<QiEHaoVideoCategoryItem>[] = [];
  [platformNames.AiQiYi]: CascadingPlatformDataItem<AiQiYiVideoCategoryItem>[] = [];
  [platformNames.WangYiHao]: CascadingPlatformDataItem<WangyiVideoCategory>[] = [];
  [platformNames.YiDianHao]: CascadingPlatformDataItem<YidianhaoCategory>[] = [];
  [platformNames.BiliBili]: CascadingPlatformDataItem<BilibiliVideoCategoryInfo>[] = [];
  [platformNames.SouHuHao]: CascadingPlatformDataItem<SouhuhaoVideoCategoryItem>[] = []
}

export class VideoTags {
  [immerable] = true;

  [index: string]: PlatformDataItem<string>[]

  [platformNames.QiEHao]: PlatformDataItem<string>[] = [];
  [platformNames.AiQiYi]: PlatformDataItem<string>[] = [];
  [platformNames.WangYiHao]: PlatformDataItem<string>[] = [];
  [platformNames.YiDianHao]: PlatformDataItem<string>[] = [];
  [platformNames.BiliBili]: PlatformDataItem<string>[] = []
}

export class VideoContentViewModel {
  [immerable] = true

  public aPlatformForm: APlatformFormViewModel = new APlatformFormViewModel()
  public bPlatformForm: BPlatformFormViewModel = new BPlatformFormViewModel()
  public isOriginal = false
  public location: {
    [key: string]: PlatformDataItem | null
  } = {}
  public timing?: TimeStamp
  public categories: VideoCategories = new VideoCategories()
}

export class APlatformFormViewModel {
  [immerable] = true

  public title = ''
  public description = ''
}

export class BPlatformFormViewModel {
  get tags(): string[] {
    return this._tags
  }

  set tags(value: string[]) {
    const set = new Set(value) //去重
    this._tags = Array.from(set)
  }

  [immerable] = true

  public title = ''
  public description = ''
  private _tags: string[] = []
}

export class SingleVideoAccountViewModel {
  [immerable] = true

  public video: VideoFileInfo | null = null
  public cover: ImageFileInfo | null = null
  public accounts: SpiderAccount[] = []
}

export class MultipleVideoAccountViewModelItem {
  [immerable] = true

  public id = randomService.randomId()

  constructor(
    public video: VideoFileInfo | null,
    public cover: ImageFileInfo | null = null,
    public account: SpiderAccount | null = null,
  ) {}
}

export class MultipleVideoAccountViewModel {
  [immerable] = true

  public items: MultipleVideoAccountViewModelItem[] = []
}
