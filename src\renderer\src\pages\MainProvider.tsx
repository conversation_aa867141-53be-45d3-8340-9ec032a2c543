import type { ReactElement } from 'react'
import { Fragment, useCallback, useEffect } from 'react'
import { useState } from 'react'
import { UserContextPreload } from '@renderer/components/preload/UserContextPreload'
import { CurrentTeamContextPreload } from '@renderer/components/preload/CurrentTeamContextPreload'
import { TokenPreload } from '@renderer/components/preload/TokenPreload'
import { CurrentTeamPreload } from '@renderer/components/preload/CurrentTeamPreload'
import { useInnerVipStore } from '@renderer/store/vipStore'
import { VipExpiredDialog } from '@renderer/components/vip/VipExpiredDialog'
import { CustomerServiceDialog } from '@renderer/components/vip/CustomerServiceDialog'
import {
  MemberDatabasePreload,
  UserDatabasePreload,
} from '@renderer/components/preload/DatabasePreload'
import { type MainDialogProps, noop, SystemProvider } from './context'
import { dialogPropsMap } from './constant'
import { Dialog, DialogContent } from '@renderer/shadcn-components/ui/dialog'
import { isFragment } from 'react-is'
import { AccountStateFloatingWindow } from '@renderer/components/AccountState/AccountStateFloatingWindow'
import { usePushLimitsComponents } from '@renderer/hooks/application/use-push-limits'
import { VipDialogProvider } from './Vip/vipDialog'
import { CountExhaustDialogWrapperRef } from '@renderer/components/vip/CountExhaustDialog'
import { AddSpace } from '@renderer/pages/Space/components/AddSpace'
import { AddAccountDialog } from './PlatformAuthorization/Accounts'
import { PhoneBindingReminderWrapper } from '@renderer/components/PhoneBindingReminderWrapper'
import { PublishVideoBeforeDialog } from './Publish/video/beforeDialog'
import { AuthorizeSubAccountFromWechat3rdPartyDialog } from '@renderer/pages/PlatformAuthorization/authorize-sub-account-from-wechat3rd-party-dialog'
import { Layout } from './Layout'
import { Wechat3rdPartyAuthorizeDialog } from './PlatformAuthorization/Wechat3rdPartyAuthorizeDialog'
import { SessionCheckContextProvider } from '@renderer/components/session-check/context/session-check-context-provider'
import { SystemNotifyDialog } from '@renderer/components/system-notify/system-notify'
import { AddWechatShiPinHaoDialog } from './PlatformAuthorization/AddWechatShiPinHaoDialog'

export function MainProvider(): ReactElement {
  // const [openVipUpgradeDialog, setOpenVipUpgradeDialog] = useState(false)
  const [openVipExpiredDialog, setOpenVipExpiredDialog] = useState(false)
  const [openCustomereDialog, setOpenCustomereDialog] = useState(false)
  const { CountExhaustDialog } = usePushLimitsComponents()

  const [state, setState] = useState(useInnerVipStore.getState())

  useEffect(() => {
    const unsubscribe = useInnerVipStore.subscribe((newState) => {
      setState(newState)
    })
    return () => unsubscribe()
  }, [])

  useEffect(() => {
    if (state.isNewEvent && !state.vip) {
      setOpenVipExpiredDialog(true)
      state.setNewEvent(false)
    }
  }, [state, state.isNewEvent, state.vip])

  /**
   * 全局Dialog
   */
  const [openDialog, setOpenDialog] = useState<MainDialogProps>({
    open: false,
    data: {
      Element: Fragment,
    },
  })

  /**
   * 全局Dialog
   */
  const [openDialogSub, setOpenDialogSub] = useState<MainDialogProps>({
    open: false,
    data: {
      Element: Fragment,
    },
  })

  /**
   * 设置Dialog 设置
   */
  const onSetDialog = useCallback(
    (callback: (props: typeof dialogPropsMap) => Partial<MainDialogProps['data']>) => {
      setOpenDialog(({ open, ...rest }) => {
        if (open) {
          return {
            ...rest,
            data: {
              Element: Fragment,
            },
            open: false,
          }
        }

        return {
          ...rest,
          data: (callback(dialogPropsMap) as MainDialogProps['data']) ?? {
            Element: Fragment,
          },
          open: !open,
        }
      })
    },
    [],
  )

  /**
   * 设置Dialog 设置
   */
  const onSetDialogSub = useCallback(
    (callback: (props: typeof dialogPropsMap) => Partial<MainDialogProps['data']>) => {
      setOpenDialogSub(({ open, ...rest }) => {
        return {
          ...rest,
          data: (callback(dialogPropsMap) as MainDialogProps['data']) ?? {
            Element: Fragment,
          },
          open: !open,
        }
      })
    },
    [],
  )

  const {
    Element: DialogChildren,
    dialogProps,
    dialogContentProps,
    elementProps: { onClose: dialogOnClose, ...dialogElementProps } = {},
  } = openDialog.data
  const {
    Element: DialogChildrenSub,
    dialogProps: dialogPropsSub,
    dialogContentProps: dialogContentPropsSub,
    elementProps: { onClose: dialogOnCloseSub, ...dialogElementSubProps } = {},
  } = openDialogSub.data

  return (
    <SystemProvider
      value={{
        openDialog,
        openDialogSub,

        onSetDialog,
        onSetDialogSub,
      }}
    >
      <TokenPreload>
        <VipDialogProvider>
          <CountExhaustDialogWrapperRef />
          <UserContextPreload>
            <UserDatabasePreload>
              <CurrentTeamPreload>
                <MemberDatabasePreload>
                  <CurrentTeamContextPreload>
                    <PublishVideoBeforeDialog>
                      <SessionCheckContextProvider>
                        <Layout />
                        <PhoneBindingReminderWrapper />
                        <Dialog
                          open={openDialog.open}
                          onOpenChange={(open) => {
                            dialogOnClose?.()
                            onSetDialog(() => ({
                              open,
                              Element: () => null,
                            }))
                          }}
                          {...dialogProps}
                        >
                          <DialogContent
                            aria-description="content"
                            aria-describedby="content"
                            className="w-[auto]"
                            {...dialogContentProps}
                          >
                            {isFragment(<DialogChildren />) ? (
                              <DialogChildren />
                            ) : (
                              <DialogChildren
                                onClose={() => {
                                  dialogOnClose?.()
                                  onSetDialog(noop)
                                }}
                                {...dialogElementProps}
                              />
                            )}
                          </DialogContent>
                        </Dialog>

                        <Dialog
                          onOpenChange={(open) => {
                            dialogOnCloseSub?.()
                            onSetDialogSub(() => ({
                              open,
                              Element: () => null,
                            }))
                          }}
                          {...dialogPropsSub}
                          open={openDialogSub.open}
                        >
                          <DialogContent
                            aria-description="content"
                            aria-describedby="content"
                            className="w-[auto]"
                            {...dialogContentPropsSub}
                          >
                            {isFragment(<DialogChildrenSub />) ? (
                              <DialogChildrenSub />
                            ) : (
                              <DialogChildrenSub
                                onClose={() => {
                                  dialogOnCloseSub?.()
                                  onSetDialogSub(noop)
                                }}
                                {...dialogElementSubProps}
                              />
                            )}
                          </DialogContent>
                        </Dialog>
                        <VipExpiredDialog
                          open={openVipExpiredDialog}
                          openChange={setOpenVipExpiredDialog}
                        />
                        <CustomerServiceDialog
                          open={openCustomereDialog}
                          openChange={setOpenCustomereDialog}
                        ></CustomerServiceDialog>
                        <AccountStateFloatingWindow />
                        {CountExhaustDialog}

                        <AddSpace />
                        <AddAccountDialog />
                        <AuthorizeSubAccountFromWechat3rdPartyDialog />
                        <Wechat3rdPartyAuthorizeDialog />
                        <SystemNotifyDialog />
                        <AddWechatShiPinHaoDialog />
                      </SessionCheckContextProvider>
                    </PublishVideoBeforeDialog>
                  </CurrentTeamContextPreload>
                </MemberDatabasePreload>
              </CurrentTeamPreload>
            </UserDatabasePreload>
          </UserContextPreload>
        </VipDialogProvider>
      </TokenPreload>
    </SystemProvider>
  )
}
