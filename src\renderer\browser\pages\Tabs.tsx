import { TabContextMenu } from '@browser/pages/components/TabContextMenu'
import { useTabStore } from '@browser/store/tabStore'
import CloseIcon from '@browser/assets/close.svg?react'
import { Fragment, useEffect, useRef } from 'react'
import { useBrowserService } from '@browser/infrastructure/services/browser-service'

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@renderer/shadcn-components/ui/tooltip'
import { TabHoverCard } from './components/TabHoverCard'

export function Tabs() {
  const { tabs, activeTab } = useTabStore()
  const browserService = useBrowserService()
  const containerRef = useRef<HTMLDivElement>(null)
  const tabRefs = useRef<(HTMLDivElement | null)[]>([])
  const scrollPosition = useRef(0)
  const isScrolling = useRef(false)

  useEffect(() => {
    const container = containerRef.current
    if (container) {
      const handleWheel = (event: WheelEvent) => {
        if (event.deltaY !== 0) {
          event.preventDefault()
          const maxScrollLeft = container.scrollWidth - container.clientWidth
          scrollPosition.current = Math.min(
            Math.max(scrollPosition.current + event.deltaY, 0),
            maxScrollLeft,
          )
          if (!isScrolling.current) {
            isScrolling.current = true
            requestAnimationFrame(smoothScroll)
          }
        }
      }

      const smoothScroll = () => {
        if (container) {
          container.scrollLeft += (scrollPosition.current - container.scrollLeft) * 0.1
          if (Math.abs(scrollPosition.current - container.scrollLeft) > 1) {
            requestAnimationFrame(smoothScroll)
          } else {
            isScrolling.current = false
          }
        }
      }

      container.addEventListener('wheel', handleWheel)
      return () => container.removeEventListener('wheel', handleWheel)
    }

    return
  }, [])

  useEffect(() => {
    const container = containerRef.current
    if (container && activeTab) {
      const activeTabIndex = tabs.findIndex((tab) => tab.id === activeTab.id)
      const activeTabElement = tabRefs.current[activeTabIndex]
      if (activeTabElement) {
        const containerRect = container.getBoundingClientRect()
        const activeTabRect = activeTabElement.getBoundingClientRect()

        if (activeTabRect.left < containerRect.left || activeTabRect.right > containerRect.right) {
          activeTabElement.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest',
          })
        }
      }
    }
  }, [activeTab, tabs])

  return (
    <div
      ref={containerRef}
      className="mx-2 flex flex-grow overflow-x-scroll"
      style={{
        scrollbarWidth: 'none',
      }}
    >
      <div>
        <div className="mr-1.5 flex h-[28px] items-center">
          <div className="mt-3 h-4 w-0.5 rounded"></div>
        </div>
        {tabs[0]?.id === activeTab?.id && (
          <div className="relative h-3 flex-grow bg-white">
            <div className="absolute left-0 top-0 h-3 w-full rounded-br-lg bg-secondary"></div>
          </div>
        )}
      </div>
      {tabs.map((tab, index) => {
        const active = activeTab?.id === tab.id
        const nextActive = tabs[index + 1]?.id === activeTab?.id
        return (
          <Fragment key={tab.id}>
            <TabContextMenu tab={tab}>
              <TooltipProvider>
                <Tooltip
                  delayDuration={600}
                  onOpenChange={(open) => {
                    if (open) {
                      browserService.setHeaderViewTop()
                    } else {
                      browserService.setTabViewTop()
                    }
                  }}
                >
                  <TooltipTrigger asChild>
                    <div ref={(el) => (tabRefs.current[index] = el)}>
                      <div
                        className={`mt-1.5 flex h-[28px] w-full items-center rounded-lg ${active ? 'rounded-b-none bg-white shadow-2xl' : 'bg-secondary hover:bg-accent'}`}
                        onClick={() => {
                          void browserService.activeTab(tab.id)
                        }}
                      >
                        {tab.icon ? (
                          <div className="relative mx-2 h-4 w-4 flex-shrink-0">
                            <img src={tab.icon} alt="" className="h-4 w-4 rounded" />
                            <div
                              className="absolute -left-1 -top-1 h-2 w-2 rounded-full"
                              style={{
                                backgroundColor: tab.contextColor,
                              }}
                            ></div>
                          </div>
                        ) : (
                          <div
                            className="mx-2 h-4 w-4 flex-shrink-0 rounded-full"
                            style={{
                              backgroundColor: tab.contextColor,
                            }}
                          ></div>
                        )}
                        <div
                          className="flex w-0 min-w-2 flex-grow items-center text-xs"
                          style={{
                            position: 'relative',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                            maskImage: 'linear-gradient(to right, black 70%, transparent)',
                          }}
                        >
                          {!tab.url.includes(tab.contextName)
                            ? `${tab.contextName} - ${tab.title}`
                            : tab.title}
                        </div>
                        <div
                          className="mx-2 flex h-4 w-4 flex-shrink-0 items-center justify-center rounded-full hover:bg-gray-300"
                          onClick={(e) => {
                            e.stopPropagation()
                            void browserService.closeTab(tab.id)
                          }}
                        >
                          <CloseIcon className="h-2 w-2" />
                        </div>
                      </div>
                      {active && <div className="h-1.5 flex-grow bg-white"></div>}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent
                    align="center"
                    className="bg-white text-stone-950 shadow-xl"
                    style={{ border: '1px solid #D1D5DB' }}
                  >
                    <TabHoverCard title={tab.title} contextName={tab.contextName} />
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </TabContextMenu>
            {index < tabs.length - 1 && (
              <div>
                <div className="mx-1.5 flex h-[28px] items-center">
                  <div className="mt-3 h-4 w-0.5 rounded bg-gray-300"></div>
                </div>
                {active && (
                  <div className="relative h-3 flex-grow bg-white">
                    <div className="absolute left-0 top-0 h-3 w-full rounded-bl-lg bg-secondary"></div>
                  </div>
                )}
                {nextActive && (
                  <div className="relative h-3 flex-grow bg-white">
                    <div className="absolute left-0 top-0 h-3 w-full rounded-br-lg bg-secondary"></div>
                  </div>
                )}
              </div>
            )}
          </Fragment>
        )
      })}

      <div>
        <div className="ml-1.5 flex h-[28px] items-center">
          <div className="mt-3 h-4 w-0.5 rounded"></div>
        </div>
        {tabs[tabs.length - 1]?.id === activeTab?.id && (
          <div className="relative h-3 flex-grow bg-white">
            <div className="absolute left-0 top-0 h-3 w-full rounded-bl-lg bg-secondary"></div>
          </div>
        )}
      </div>
    </div>
  )
}
