import { useAccountCheckService } from '@renderer/infrastructure/services/application-service/accountCheck-service'
import { Button } from '@renderer/shadcn-components/ui/button'
import { useCallback, useEffect, useState } from 'react'
import DeleteFIcon from '@renderer/assets/accounts/deleteF.svg?react'
import SetFIcon from '@renderer/assets/accounts/setF.svg?react'
import SetDIcon from '@renderer/assets/accounts/setD.svg?react'
import { AccountIdSelectorDialog } from '@renderer/components/AccountSelector/account-id-selector-dialog'
import { useTeamService } from '@renderer/infrastructure/services'
import { toast } from 'sonner'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { platformNameMap } from '@renderer/infrastructure/model'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@renderer/shadcn-components/ui/tooltip'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@renderer/shadcn-components/ui/dialog'
import { Input } from '@renderer/shadcn-components/ui/input'
import { LoadingButton } from '../LoadingButton'
import { useMemberApi } from '@renderer/infrastructure/services/entity-service'

export function SheetOperator({
  accountCount,
  memberId,
  userId,
  maxAccountCount,
}: {
  accountCount: number
  memberId: string
  userId: string
  maxAccountCount: number
}) {
  const [openSetMaxAccountCount, setOpenSetMaxAccountCount] = useState(false)
  const memberApi = useMemberApi()
  const queryClient = useQueryClient()
  const teamService = useTeamService()
  const [openDialog, setOpenDialog] = useState(false)
  const [operators, setOperators] = useState<
    Awaited<ReturnType<typeof accountCheckService.getPlatformAccountsByMemberId>>
  >([])
  const accountCheckService = useAccountCheckService()
  const [newMaxAccountCount, setNewMaxAccountCount] = useState(maxAccountCount)

  const query = useQuery({
    queryKey: ['members-max-account-count', newMaxAccountCount],
    queryFn: async () => {
      await memberApi.setMaxAccountCount(userId, newMaxAccountCount)
      return true
    },
    enabled: false,
  })

  const init = useCallback(async () => {
    const result = await accountCheckService.getPlatformAccountsByMemberId(memberId)
    setOperators(result)
  }, [accountCheckService, memberId])

  useEffect(() => {
    init()
  }, [init])

  return (
    <div className="flex h-full flex-col overflow-hidden">
      <div className="p-4">
        <div className="flex justify-between">
          <Button onClick={() => setOpenDialog(true)}>添加账号</Button>
          <Dialog open={openSetMaxAccountCount} onOpenChange={setOpenSetMaxAccountCount}>
            <DialogTrigger asChild>
              <Button variant="link">设置账号上限</Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>设置账号上限</DialogTitle>
                <DialogDescription>
                  为成员设置最大运营账号数量, 如果当前运营账号数量大于设置的数量, 则会设置失败
                </DialogDescription>
              </DialogHeader>
              <div className="flex flex-col gap-2">
                <Input
                  type="number"
                  value={newMaxAccountCount}
                  onChange={(event) => {
                    const value = Number(event.target.value)
                    if (value < 0) {
                      setNewMaxAccountCount(0)
                      return
                    }
                    setNewMaxAccountCount(value)
                  }}
                  placeholder="请输入最大运营账号数量"
                />
                <span className="text-sm text-[#999999]">当前账号数: {accountCount}</span>
              </div>
              <DialogFooter className="sm:justify-end">
                <DialogClose asChild>
                  <Button type="button" variant="outline">
                    取消
                  </Button>
                </DialogClose>
                <LoadingButton
                  isPending={query.isLoading}
                  type="button"
                  onClick={async () => {
                    const { isError } = await query.refetch()
                    if (!isError) {
                      setOpenSetMaxAccountCount(false)
                      queryClient.invalidateQueries({ queryKey: ['members'] })
                      toast.success('设置成功')
                    }
                  }}
                >
                  保存
                </LoadingButton>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        <AccountIdSelectorDialog
          open={openDialog}
          onOpenChange={setOpenDialog}
          selectedAccountIds={operators.map((item) => item.id)}
          onChange={async (accountIds) => {
            await teamService.setMemberAccounts(userId, accountIds)
            init()
            void queryClient.invalidateQueries({
              queryKey: ['members'],
            })
          }}
          multiple={true}
          selectable={(account) => {
            return operators.every((item) => item.id !== account.accountId)
          }}
        />
      </div>

      <div className="flex-1 overflow-auto pb-4">
        {operators.map((item) => {
          return (
            <div
              key={item.id}
              className="group mx-3 flex h-[64px] items-center gap-2 rounded-lg px-3 hover:bg-[#F8F8FA]"
            >
              <div className="relative">
                <img src={item.avatar} alt="" className="h-[32px] w-[32px] rounded-full" />
                <img
                  className="absolute -bottom-1 -right-1 h-[18px] w-[18px] rounded-full"
                  alt=""
                  src={platformNameMap[item.platformName].icon}
                />
              </div>

              <span className="truncate">{item.name}</span>
              {item.principalId === memberId && (
                <span className="flex-shrink-0 rounded-sm bg-[#E5E3FB] px-2 py-[2px] text-[12px] text-[#4E3EE9]">
                  负责人
                </span>
              )}
              <div className="hidden flex-1 justify-end gap-1 group-hover:flex">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div>
                        {item.principalId === memberId ? (
                          <SetDIcon
                            className="cursor-pointer text-[#********] hover:text-[#222222]"
                            onClick={async () => {
                              await accountCheckService.deletePrincipal(item.id)
                              init()
                            }}
                          />
                        ) : (
                          <SetFIcon
                            className="cursor-pointer text-[#********] hover:text-[#222222]"
                            onClick={async () => {
                              try {
                                await accountCheckService.pubPrincipal(item.id, memberId)
                                init()
                              } catch (error) {
                                toast.error((error as Error).message)
                              }
                            }}
                          />
                        )}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent className="bg-[#000000db] text-[#fff]">
                      {item.principalId === memberId ? '取消负责人' : '设置负责人'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <DeleteFIcon
                  className="cursor-pointer"
                  onClick={async () => {
                    await teamService.setMemberAccounts(
                      userId,
                      operators.map((op) => op.id).filter((id) => id !== item.id),
                    )
                    init()
                    void queryClient.invalidateQueries({
                      queryKey: ['members'],
                    })
                  }}
                />
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
