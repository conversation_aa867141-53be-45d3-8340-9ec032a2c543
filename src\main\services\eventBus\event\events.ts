export const browserEvents = {
  headerInitialed: 'HeaderInitialed',
  tabsInContextIncreased: 'tabsInContextIncreased',
  tabsInContextDecreased: 'tabsInContextDecreased',
  accountSessionStateChanged: 'accountSessionStateChanged',
  WebSpaceAuthorized: 'WebSpaceAuthorized',
  identifyVerified: 'identifyVerified',
  tabClosed: 'tabClosed',
  accountContextUpdated: 'accountContextUpdated',
}

export const accountContextEvents = {
  sessionStateChange: 'sessionStateChange',
}

export const publishEvents = {
  getAccountSession: 'publish.getAccountSession',
  getAccountSessionReply: 'publish.getAccountSessionReply',
  getAccountSessionFailed: 'publish.getAccountSessionFailed',
}
