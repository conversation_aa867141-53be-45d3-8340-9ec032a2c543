import { AuthorizingAccountInfo } from '@main/model/authorizing-account-info'
import { PlatformService } from '../index'
import { platformNames } from '@common/model/platform-name'
import { getPlatformServicePromise } from '../import-promise'
import type { ShipinhaoVideoTaskInput } from '@yixiaoer/platform-service/dist/media-platform/shipinhao/shipinhao-video.service'
import type { AccountSession, MusicPlatformDataItem, PlatformDataItem } from '@common/structure'
import {
  type DataColumn,
  Platforms,
  type ShipinhaoDynamicTaskInput,
} from '@yixiaoer/platform-service'
import type { PlatformMusicInfo } from '@yixiaoer/platform-service/dist/media-platform/model'
import { eventBus } from '@main/services/eventBus/eventBus'
import { publishEvents } from '@main/services/eventBus/event/events'
import { identifierService } from '@common/infrastructure/services/identifier-service'

async function getCookies(
  cookies: Electron.Cookie[],
  accountId: string,
  wechatLockToken: [string, string] | null = null,
) {
  // 如果是第三方授权账号，cookies会为空，需要从主窗口获取
  if (cookies.length === 0) {
    if (!wechatLockToken) throw new Error('没有微信锁token，无法查询cookie')

    const requestId = identifierService.generateUUID()
    eventBus.emit(publishEvents.getAccountSession, requestId, accountId, wechatLockToken[1])

    const session = await new Promise<AccountSession>((resolve, reject) => {
      const offReply = eventBus.on(
        publishEvents.getAccountSessionReply,
        (event_requestId, session) => {
          if (event_requestId !== requestId) return
          resolve(session)
          cleanup()
        },
      )
      const offFailed = eventBus.on(publishEvents.getAccountSessionFailed, (event_requestId) => {
        if (event_requestId !== requestId) return
        reject(new Error('获取账号会话失败'))
        cleanup()
      })

      // 统一的清理函数
      function cleanup() {
        offReply()
        offFailed()
      }
    })
    cookies = session.cookies
  }
  return cookies
}

class WeiXinShiPinHaoPlatformService extends PlatformService {
  constructor() {
    super(platformNames.WeiXinShiPinHao)
  }

  async pushVideo(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
    wechatLockToken: [string, string] | null,
  ): Promise<string> {
    cookies = await getCookies(cookies, accountId, wechatLockToken)
    console.debug('发布开始', accountId)
    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(
      async (eventEmitter) =>
        (await getPlatformServicePromise()).Shipinhao.publishVideo(
          cookie,
          body as ShipinhaoVideoTaskInput,
          eventEmitter,
        ),
      progressCallBack,
    )

    console.debug('发布完成', accountId)

    return result.publishId!
  }

  // 推送图文
  async pushDynamic(
    contentTypeName: string,
    accountId: string,
    authorId: string,
    cookies: Electron.Cookie[],
    body: unknown,
    progressCallBack: (progress: number, message: string) => void,
    wechatLockToken: [string, string] | null,
  ): Promise<string> {
    cookies = await getCookies(cookies, accountId, wechatLockToken)

    console.debug('发布开始', accountId)

    const cookie = this.convertCookie(cookies)
    const result = await this.getPushingResult(
      async (eventEmitter) =>
        (await getPlatformServicePromise()).Shipinhao.publishDynamic(
          cookie,
          body as ShipinhaoDynamicTaskInput,
          eventEmitter,
        ),
      progressCallBack,
    )

    console.debug('发布完成', accountId)

    return result.publishId!
  }

  async getAccountInfo(cookies: Electron.Cookie[]): Promise<AuthorizingAccountInfo> {
    const cookie = this.convertCookie(cookies)

    const info = await this.getData(
      async () => (await getPlatformServicePromise()).Shipinhao.getShipinhaoUserInfo(cookie),
      (x) => x.data!,
    )
    return new AuthorizingAccountInfo(
      platformNames.WeiXinShiPinHao,
      info.finderUser!.uniqId,
      info.finderUser!.nickname,
      info.finderUser!.headImgUrl,
    )
  }

  async getTopics(cookies: Electron.Cookie[], keyWord: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Shipinhao.getShipinhaoTopicList(
          keyWord,
          this.convertCookie(cookies),
        ),
      (x) => x.topics ?? [],
    )

    return result.map((item) => ({
      id: item.id,
      text: item.name,
      raw: item,
    }))
  }

  async getFriends(cookies: Electron.Cookie[], keyWord: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Shipinhao.getShipinhaoFriend(
          this.convertCookie(cookies),
          keyWord,
        ),
      (x) => x.data?.list ?? [],
    )

    return result.map((item) => ({
      id: item.username,
      text: item.nickName,
      raw: item,
    }))
  }

  async getLocations(cookies: Electron.Cookie[], keyWord: string): Promise<PlatformDataItem[]> {
    const result = await this.getData(
      async () =>
        (await getPlatformServicePromise()).Shipinhao.getShipinhaoLocation(
          this.convertCookie(cookies),
          keyWord,
        ),
      (x) => x.data?.list ?? [],
    )

    return result.map((item) => ({
      id: item.uid,
      text: item.name,
      raw: item,
    }))
  }

  async queryAccountOverview(cookies: Electron.Cookie[]): Promise<{
    video?: DataColumn[]
    dynamic?: DataColumn[]
    article?: DataColumn[]
  }> {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    const [video, dynamic] = await Promise.all([
      platformService.DataService.getAccountReport(Platforms.ShiPinHao, cookie),
      platformService.DataService.getAccountReport(Platforms.ShiPinHao, cookie, 'dynamic'),
    ])

    return {
      video: video.data || [],
      dynamic: dynamic.data || [],
    }
  }

  async queryPublishOverview(cookies: Electron.Cookie[]) {
    const cookie = this.convertCookie(cookies)

    const platformService = await getPlatformServicePromise()

    const videoOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.ShiPinHao,
          false,
          cookie,
          'video',
        ),
      (x) => x.data || [],
    )
    const dynamicOverviews = await this.getData(
      async () =>
        await platformService.DataService.getAccountContentList(
          Platforms.ShiPinHao,
          false,
          cookie,
          'dynamic',
        ),
      (x) => x.data || [],
    )
    return [...videoOverviews, ...dynamicOverviews]
  }

  async getMusicList(
    cookies: Electron.Cookie[],
    keyWord: string,
    nextPage?: {
      nextPageParam: string
      page: number
    },
  ) {
    console.log('getMusicList', keyWord, nextPage)
    return await this.getData(
      async () =>
        (await getPlatformServicePromise()).Shipinhao.getShipinhaoMusicList(
          this.convertCookie(cookies),
          keyWord ? 'search' : 'recommend',
          keyWord,
          nextPage?.page,
          nextPage?.nextPageParam,
          5,
        ),
      (x) => {
        const totalPage = x.total! / 5
        return {
          items:
            x.data?.map(
              (item) =>
                ({
                  id: item.id,
                  title: item.name,
                  artist: item.authorName,
                  duration: item.duration / 1000,
                  url: item.url,
                  raw: item,
                }) satisfies MusicPlatformDataItem<PlatformMusicInfo>,
            ) ?? [],
          nextPage: {
            nextPageParam: x.nextPageParam,
            page: nextPage ? nextPage.page + 1 : 2,
          },
          hasMore: nextPage ? nextPage.page < totalPage : true,
        }
      },
    )
  }
}

export const weiXinShiPinHaoPlatformService = new WeiXinShiPinHaoPlatformService()
