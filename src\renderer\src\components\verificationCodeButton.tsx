import { Button } from '@renderer/shadcn-components/ui/button'
import { useEffect, useMemo, useState } from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { ReloadIcon } from '@radix-ui/react-icons'
import { useApiMutation } from '@renderer/hooks/useApiQuery'
import { phoneRegex } from '@renderer/utils/zod'
import { cn } from '@renderer/lib/utils'
export function VerificationCodeButton({
  sence = 'auth',
  phoneName = 'phone',
  className,
}: {
  sence?: 'auth' | 'resetPassword' | 'changePhone' | 'checkPhone' | 'bindPhone' | 'bindAccount'
  className?: string
  phoneName?: string
}) {
  const [countdown, setCountdown] = useState(0) // 倒计时秒数
  const [isButtonDisabled, setIsButtonDisabled] = useState(false) // 按钮状态
  const { control } = useFormContext()
  const phone = useWatch({ control, name: phoneName })

  const mutation = useApiMutation<{ phone: string; sence: string }, void>(
    (params) => ({
      url: '/users/sms-code',
      method: 'POST',
      data: params,
    }),
    {
      onSuccess: () => {
        setIsButtonDisabled(true) // 禁用按钮
        setCountdown(60) // 开始 60 秒倒计时
        // 发送验证码成功
      },
      onError: (error) => {
        // 发送验证码失败
        console.log(error)
      },
    },
  )

  const disabled = useMemo(
    () => !phoneRegex.test(phone) || isButtonDisabled,
    [phone, isButtonDisabled],
  )

  // 倒计时逻辑
  useEffect(() => {
    if (countdown > 0) {
      const timer = setInterval(() => {
        setCountdown((prev) => prev - 1)
      }, 1000) // 每秒更新一次
      return () => clearInterval(timer) // 清除定时器
    } else {
      setIsButtonDisabled(false) // 倒计时结束，启用按钮
    }
    return () => {}
  }, [countdown]) // 当 countdown 变化时运行

  const requestVerificationCode = () => {
    if (isButtonDisabled) return // 防止重复点击
    // 这里模拟发送验证码请求
    mutation.mutate({
      phone,
      sence: sence,
    })
  }
  return (
    <div
      className={cn('absolute right-2 top-0 flex h-full items-center justify-center', className)}
    >
      <Button
        onClick={requestVerificationCode}
        disabled={disabled || mutation.isPending}
        type="button"
        className="h-full px-2"
        variant="ghost"
      >
        {mutation.isPending && <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />}
        {isButtonDisabled ? `重新获取 (${countdown}s)` : '获取验证码'}
      </Button>
    </div>
  )
}
