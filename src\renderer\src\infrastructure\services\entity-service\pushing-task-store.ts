import { PushingTask } from '../../model/pushing-task'
import type { PushingTaskRepository } from '../../repository/repositories'
import { usePushingTaskRepository } from '../../repository/repositories'
import type { PushingTaskStoreObject } from '../../entity'
import { getPlatformByName } from '../../model/platforms'
import {
  getEditContentTypeByPushContentType,
  getPushContentTypeByName,
} from '../../model/contentTypes'
import { useMemo } from 'react'

export function usePushingTaskStore() {
  const pushingTaskRepository = usePushingTaskRepository()
  return useMemo(() => new PushingTaskStore(pushingTaskRepository), [pushingTaskRepository])
}

export class PushingTaskStore {
  constructor(private pushingTaskRepository: PushingTaskRepository) {}

  private _Model2Entity(pushingTask: PushingTask) {
    return {
      taskId: pushingTask.taskId,
      platformName: pushingTask.platform.name,
      accountId: pushingTask.accountId,
      contentTypeName: pushingTask.pushContentType,
      pushingConfigId: pushingTask.pushingConfigId,
      thumbUrl: pushingTask.thumbUrl,
      brief: pushingTask.brief,
      content: pushingTask.content,
      createTime: pushingTask.createTime,
      timming: pushingTask.timming,
    } satisfies PushingTaskStoreObject as PushingTaskStoreObject
  }

  private _Object2Model(object: PushingTaskStoreObject) {
    return new PushingTask(
      object.taskId,
      getPlatformByName(object.platformName),
      object.accountId,
      getEditContentTypeByPushContentType(object.contentTypeName),
      getPushContentTypeByName(object.contentTypeName),
      object.pushingConfigId,
      object.thumbUrl,
      object.brief,
      object.content,
      object.createTime,
      object.timming,
    )
  }

  save(pushingTask: PushingTask) {
    return this.pushingTaskRepository.save(this._Model2Entity(pushingTask))
  }

  async get(taskId: string) {
    const storeObject = await this.pushingTaskRepository.get(taskId)
    return this._Object2Model(storeObject)
  }

  async getMany(taskIds: string[]) {
    const storeObjects = await this.pushingTaskRepository.getMany((x) =>
      x.where('taskId').anyOf(taskIds).sortBy('createTime'),
    )
    return storeObjects.map(this._Object2Model)
  }

  async getEntireTasks() {
    return (await this.pushingTaskRepository.getMany()).map(this._Object2Model)
  }

  async deleteTasks(taskIds: string[]) {
    await this.pushingTaskRepository.bulkDelete(taskIds) //这里应该使用x.Identifier，但是目前因为取出来的是Object，没有这个属性。需要梳理Object和Entity的关系，甚至做自动转换，数据库取出来的应该都是Entity
  }

  async getByAccount(accountId: string) {
    const storeObjects = await this.pushingTaskRepository.getMany((x) =>
      x.where('accountId').equals(accountId).sortBy('createTime'),
    )
    return storeObjects.map(this._Object2Model)
  }

  async exist(taskId: string) {
    return !!(await this.pushingTaskRepository.count((x) => x.where('taskId').equals(taskId)))
  }
}
